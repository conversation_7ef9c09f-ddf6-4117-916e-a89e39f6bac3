import { SlashCommandBuilder, EmbedBuilder } from 'discord.js';

export default {
    data: new SlashCommandBuilder()
        .setName('instagram')
        .setDescription('Sends a link to the Instagram account.'),
    async execute(interaction) {
        const instagramEmbed = new EmbedBuilder()
            .setColor('#ffa300') // Using the same color as TikTok command
            .setTitle('Our Instagram Profile!')
            .setURL('https://www.instagram.com/buyboosting_com?igsh=NmFzeDhyMWh5OWY5')
            .setDescription('Check out our latest posts on Instagram and follow us!\nClick the title to go directly to the profile.')
            .setThumbnail('https://i.imgur.com/ieq27bK.png') // User specified logo
            .addFields({ name: 'Instagram Profile', value: '[@buyboosting_com](https://www.instagram.com/buyboosting_com?igsh=NmFzeDhyMWh5OWY5)' })
            .setTimestamp()
            .setFooter({ text: 'BuyBoosting Instagram', iconURL: 'https://i.imgur.com/ieq27bK.png' }); // User specified logo

        await interaction.reply({ embeds: [instagramEmbed] });
    },
};