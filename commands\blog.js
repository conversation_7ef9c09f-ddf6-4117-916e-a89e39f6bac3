import { SlashCommandBuilder, EmbedBuilder } from 'discord.js';

export default {
    data: new SlashCommandBuilder()
        .setName('blog')
        .setDescription('Sends a link to the website\'s blog posts.'),
    async execute(interaction) {
        const blogEmbed = new EmbedBuilder()
            .setColor('#ffa300') // Using the same color
            .setTitle('Our Blog Posts!')
            .setURL('https://buyboosting.com/blog')
            .setDescription('Check out our latest articles and news on our blog!\nClick the title to go directly to the blog.')
            .setThumbnail('https://i.imgur.com/wEjKsL5.png') // User specified logo
            .addFields({ name: 'Blog', value: '[Visit our Blog](https://buyboosting.com/blog)' })
            .setTimestamp()
            .setFooter({ text: 'BuyBoosting Blog', iconURL: 'https://i.imgur.com/wEjKsL5.png' }); // User specified logo

        await interaction.reply({ embeds: [blogEmbed] });
    },
};