// ticketManager/ticketChannels.js
import { ChannelType, PermissionsBitField } from 'discord.js';
import { logTicketAction } from './ticketLogging.js';

/**
 * Sanitizes a username to be used in a Discord channel name.
 * Converts to lowercase, replaces spaces and multiple hyphens, removes most special characters.
 * @param {string} username The username to sanitize.
 * @returns {string} The sanitized username.
 */
export function sanitizeUsernameForChannel(username) {
    return username
        .toLowerCase()
        .replace(/\s+/g, '-')
        .replace(/[^a-z0-9-]/g, '')
        .replace(/-+/g, '-')
        .substring(0, 32);
}

/**
 * Finds an existing ticket category or creates a new one.
 * @param {import('discord.js').Guild} guild The guild.
 * @param {import('discord.js').Client} client The Discord client.
 * @param {object} config The bot configuration.
 * @returns {Promise<import('discord.js').CategoryChannel|null>} The category channel or null if creation failed.
 */
export async function findOrCreateTicketCategory(guild, client, config) {
    let ticketCategory = guild.channels.cache.find(
        c => c.name === config.ticketCategoryName && c.type === ChannelType.GuildCategory
    );

    if (!ticketCategory) {
        try {
            ticketCategory = await guild.channels.create({
                name: config.ticketCategoryName,
                type: ChannelType.GuildCategory,
                permissionOverwrites: [
                    { id: guild.roles.everyone, deny: [PermissionsBitField.Flags.ViewChannel] },
                    { id: client.user.id, allow: [PermissionsBitField.Flags.ViewChannel, PermissionsBitField.Flags.ManageChannels, PermissionsBitField.Flags.SendMessages, PermissionsBitField.Flags.EmbedLinks] },
                ],
            });
            console.log(`Created ticket category: ${ticketCategory.name}`);
            // Assuming client.user.tag is available for logging, or adjust as needed
            await logTicketAction(client, config, `Ticket category "${ticketCategory.name}" created by ${client.user.tag}.`);
            return ticketCategory;
        } catch (error) {
            console.error('Error creating ticket category:', error);
            await logTicketAction(client, config, `ERROR: Failed to create ticket category. Error: ${error.message}`, true);
            return null;
        }
    }
    return ticketCategory;
}

/**
 * Creates a new ticket channel.
 * @param {import('discord.js').Guild} guild The guild.
 * @param {import('discord.js').User} user The user who initiated ticket creation.
 * @param {object} config The bot configuration.
 * @param {import('discord.js').CategoryChannel} ticketCategory The parent category for the ticket.
 * @param {string} channelName The name for the new channel.
 * @param {string} formattedTicketNumber The formatted ticket number.
 * @param {string} ticketSubject The subject of the ticket.
 * @param {string} ticketOrderId The order ID for the ticket.
 * @returns {Promise<import('discord.js').TextChannel|null>} The created text channel or null if creation failed.
 */
export async function createTicketChannel(guild, user, client, config, ticketCategory, channelName, formattedTicketNumber, ticketSubject, ticketOrderId) {
    try {
        const permissionOverwrites = [
            { id: guild.roles.everyone, deny: [PermissionsBitField.Flags.ViewChannel] },
            { id: user.id, allow: [PermissionsBitField.Flags.ViewChannel, PermissionsBitField.Flags.SendMessages, PermissionsBitField.Flags.ReadMessageHistory, PermissionsBitField.Flags.AttachFiles, PermissionsBitField.Flags.EmbedLinks] },
            { id: client.user.id, allow: [PermissionsBitField.Flags.ViewChannel, PermissionsBitField.Flags.SendMessages, PermissionsBitField.Flags.ReadMessageHistory, PermissionsBitField.Flags.EmbedLinks, PermissionsBitField.Flags.ManageMessages, PermissionsBitField.Flags.ManageChannels] },
        ];

        if (config.supportRoleIds && config.supportRoleIds.length > 0) {
            for (const roleId of config.supportRoleIds) {
                const role = guild.roles.cache.get(roleId);
                if (role) {
                    permissionOverwrites.push({ id: roleId, allow: [PermissionsBitField.Flags.ViewChannel, PermissionsBitField.Flags.SendMessages, PermissionsBitField.Flags.ReadMessageHistory, PermissionsBitField.Flags.AttachFiles, PermissionsBitField.Flags.EmbedLinks, PermissionsBitField.Flags.ManageMessages] });
                } else {
                    console.warn(`Support role ID ${roleId} not found in guild ${guild.id}.`);
                    await logTicketAction(client, config, `WARNING: Support role ID ${roleId} not found during ticket creation for ${user.tag}.`, true);
                }
            }
        }

        const ticketChannel = await guild.channels.create({
            name: channelName,
            type: ChannelType.GuildText,
            parent: ticketCategory,
            permissionOverwrites: permissionOverwrites,
            topic: `Ticket #${formattedTicketNumber} - Order: ${ticketOrderId} - ${ticketSubject || 'No Subject'}. Opened by ${user.tag} (${user.id}).`.substring(0, 1024),
        });
        console.log(`Created ticket channel: ${ticketChannel.name} for ${user.tag}`);
        return ticketChannel;
    } catch (error) {
        console.error(`Error creating ticket channel for ${user.tag}:`, error);
        await logTicketAction(client, config, `ERROR: Failed to create ticket channel for ${user.tag}. Error: ${error.message}`, true);
        return null;
    }
}

/**
 * Updates the topic of a ticket channel.
 * @param {import('discord.js').TextChannel} channel The ticket channel.
 * @param {string} newTopic The new topic string.
 * @param {object} config Bot configuration.
 * @param {import('discord.js').Client} client The Discord client.
 */
export async function updateTicketChannelTopic(channel, newTopic, client, config) {
    try {
        await channel.setTopic(newTopic.substring(0, 1024));
    } catch (topicError) {
        console.error(`Failed to update topic for ${channel.name}:`, topicError);
        await logTicketAction(client, config, `WARNING: Failed to update topic for ${channel.name}. Error: ${topicError.message}`, true);
    }
}


/**
 * Deletes a ticket channel.
 * @param {import('discord.js').TextChannel} channel The channel to delete.
 * @param {import('discord.js').User} user The user initiating the deletion.
 * @param {string} reason Reason for deletion.
 * @param {import('discord.js').Client} client The Discord client.
 * @param {object} config The bot configuration.
 */
export async function deleteTicketChannel(channel, user, reason, client, config) {
    try {
        await channel.delete(reason);
        console.log(`Ticket channel ${channel.name} deleted. Reason: ${reason}`);
        // Logging of successful deletion can be done by the caller (e.g., closeTicket)
    } catch (deleteError) {
        console.error(`Error deleting ticket channel ${channel.name}:`, deleteError);
        await logTicketAction(client, config, `ERROR: Failed to delete ticket channel ${channel.name} (requested by ${user.tag}). Error: ${deleteError.message}`, true);
        // Optionally, notify admin/log channel if deletion fails critically
        const logChannel = await client.channels.fetch(config.logChannelId).catch(() => null);
        if (logChannel && logChannel.isTextBased()) {
            await logChannel.send(`⚠️ Failed to delete ticket channel ${channel.name} (${channel.id}). Please check permissions or delete manually.`).catch(console.error);
        }
    }
}