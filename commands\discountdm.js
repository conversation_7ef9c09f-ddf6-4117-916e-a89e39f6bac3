import { <PERSON>lash<PERSON>ommandBuilder, MessageFlags, EmbedBuilder, PermissionsBitField } from 'discord.js';

export default {
    data: new SlashCommandBuilder()
        .setName('discountdm')
        .setDescription('Sends discount instructions DM to all members of a specified role.')
        .addRoleOption(option =>
            option.setName('role')
                .setDescription('The role to send the discount instructions to')
                .setRequired(true))
        .setDefaultMemberPermissions(0)
        .setDMPermission(false),

    async execute(interaction, client, config) {
        const targetRole = interaction.options.getRole('role');

        // Check permissions - only admins or support staff can use this command
        const isAdmin = interaction.member.permissions.has(PermissionsBitField.Flags.Administrator);
        const memberHasSupportRole = config.supportRoleIds && config.supportRoleIds.length > 0 && interaction.member.roles.cache.some(role => config.supportRoleIds.includes(role.id));

        if (!isAdmin && !memberHasSupportRole) {
            await interaction.reply({ content: 'You do not have permission to use this command. You need to be an Administrator or have a designated support role.', flags: [MessageFlags.Ephemeral] });
            return;
        }

        if (!targetRole) {
            await interaction.reply({ content: 'You must specify a role.', flags: [MessageFlags.Ephemeral] });
            return;
        }

        try {
            await interaction.deferReply({ flags: [MessageFlags.Ephemeral] });

            // Fetch all guild members to ensure we have the latest data
            await interaction.guild.members.fetch();
            const membersWithRole = interaction.guild.members.cache.filter(member => member.roles.cache.has(targetRole.id) && !member.user.bot);

            if (membersWithRole.size === 0) {
                await interaction.editReply({ content: `No non-bot members found in the role ${targetRole.name}.` });
                return;
            }

            let successCount = 0;
            let failCount = 0;

            // Send DMs to all members with the role
            const sendPromises = membersWithRole.map(member => {
                const discountMessage = `🎉 **20% Discount Available - BuyBoosting.com!**

Hey ${member}!

We have an exclusive **20% discount** available for you on all our boosting services! Here's how to claim it:

📋 **Step-by-Step Instructions:**
**1.** 🌐 Go to [buyboosting.com](https://buyboosting.com)
**2.** 🎯 Click "**Claim now**"
**3.** 📋 Copy the **Discount code**
**4.** 💳 Add it to the **payment page** if you want to buy a boosting

💰 **Save 20% on any boosting service!**

💡 **Pro Tips:**
• Make sure to apply the code **before** completing payment
• The discount applies to **all boosting services**
• Need help? Open a support ticket in our Discord!

Don't miss out on this amazing deal! 🚀`;

                const discountEmbed = new EmbedBuilder()
                    .setColor(config.embedColors?.default || '#ffa300')
                    .setDescription(discountMessage)
                    .setImage('https://i.imgur.com/bUsQB4i.gif') // BuyBoosting promotional image
                    .setFooter({
                        text: 'BuyBoosting.com | Premium Boosting Services'
                    })
                    .setTimestamp();

                return member.send({ embeds: [discountEmbed] })
                    .then(() => { successCount++; })
                    .catch(error => {
                        console.error(`Failed to send discount DM to ${member.user.tag} (ID: ${member.id}):`, error.message);
                        failCount++;
                    });
            });

            // Wait for all DMs to be sent
            await Promise.allSettled(sendPromises);

            // Send completion report
            await interaction.editReply({
                content: `Discount DM sending complete for role ${targetRole.name}.\n✅ Successfully sent to: **${successCount}** members.\n❌ Failed to send to: **${failCount}** members.`,
            });

        } catch (error) {
            console.error('Error executing discountdm command:', error);
            if (interaction.replied || interaction.deferred) {
                await interaction.editReply({ content: 'There was an error while executing this command.' });
            } else {
                await interaction.reply({ content: 'There was an error while executing this command!', flags: [MessageFlags.Ephemeral] });
            }
        }
    }
};
