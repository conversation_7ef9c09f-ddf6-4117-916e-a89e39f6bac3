import { <PERSON>lashCommandBuilder, MessageFlags, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } from 'discord.js';

export default {
    data: new SlashCommandBuilder()
        .setName('discountdm')
        .setDescription('Sends you a DM with step-by-step instructions on how to claim a 20% discount on BuyBoosting.com')
        .setDMPermission(true), // Allow this command to be used in DMs as well

    async execute(interaction, client, config) {
        try {
            // Defer the reply to give us time to send the DM
            await interaction.deferReply({ flags: [MessageFlags.Ephemeral] });

            // Create the discount instructions embed
            const discountEmbed = new EmbedBuilder()
                .setColor(config.embedColors?.default || '#ffa300')
                .setTitle('🎉 20% Discount Instructions - BuyBoosting.com')
                .setDescription('Follow these simple steps to claim your **20% discount** on any boosting service!')
                .addFields(
                    {
                        name: '📋 Step-by-Step Instructions:',
                        value: `
**1.** 🌐 Go to [buyboosting.com](https://buyboosting.com)
**2.** 🎯 Click "**Claim now**"
**3.** 📋 Copy the **Discount code**
**4.** 💳 Add it to the **payment page** if you want to buy a boosting

That's it! You'll save **20%** on your order! 💰`,
                        inline: false
                    },
                    {
                        name: '💡 Pro Tips:',
                        value: `
• Make sure to apply the code **before** completing payment
• The discount applies to **all boosting services**
• Need help? Open a support ticket in our Discord!`,
                        inline: false
                    }
                )
                .setThumbnail('https://i.imgur.com/JpbgHKG.png') // BuyBoosting logo
                .setImage('https://i.imgur.com/bUsQB4i.gif') // BuyBoosting promotional image
                .setFooter({ 
                    text: 'BuyBoosting.com | Premium Boosting Services', 
                    iconURL: 'https://i.imgur.com/JpbgHKG.png' 
                })
                .setTimestamp();

            // Create action buttons
            const actionRow = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setLabel('🌐 Visit BuyBoosting.com')
                        .setStyle(ButtonStyle.Link)
                        .setURL('https://buyboosting.com'),
                    new ButtonBuilder()
                        .setLabel('🎯 Claim Discount Now')
                        .setStyle(ButtonStyle.Link)
                        .setURL('https://buyboosting.com'),
                    new ButtonBuilder()
                        .setLabel('💬 Get Support')
                        .setStyle(ButtonStyle.Link)
                        .setURL('https://buyboosting.com/support')
                );

            // Try to send the DM
            try {
                await interaction.user.send({ 
                    embeds: [discountEmbed], 
                    components: [actionRow] 
                });

                // Success - confirm in the channel
                const successEmbed = new EmbedBuilder()
                    .setColor('#00ff00') // Green for success
                    .setTitle('✅ DM Sent Successfully!')
                    .setDescription(`📨 Check your DMs for detailed instructions on how to claim your **20% discount** on BuyBoosting.com!`)
                    .setFooter({ text: 'BuyBoosting.com' })
                    .setTimestamp();

                await interaction.editReply({ embeds: [successEmbed] });

            } catch (dmError) {
                console.error(`Failed to send discount DM to ${interaction.user.tag} (ID: ${interaction.user.id}):`, dmError.message);

                // DM failed - provide fallback information
                const fallbackEmbed = new EmbedBuilder()
                    .setColor('#ff6b6b') // Red for error
                    .setTitle('❌ Unable to Send DM')
                    .setDescription(`I couldn't send you a DM! Your DMs might be disabled. Here are the discount instructions:`)
                    .addFields(
                        {
                            name: '📋 How to Claim Your 20% Discount:',
                            value: `
**1.** 🌐 Go to [buyboosting.com](https://buyboosting.com)
**2.** 🎯 Click "**Claim now**"
**3.** 📋 Copy the **Discount code**
**4.** 💳 Add it to the **payment page** if you want to buy a boosting

💰 **Save 20% on any boosting service!**`,
                            inline: false
                        },
                        {
                            name: '🔧 Enable DMs for Future Messages:',
                            value: 'Go to **User Settings** → **Privacy & Safety** → Enable **Allow direct messages from server members**',
                            inline: false
                        }
                    )
                    .setThumbnail('https://i.imgur.com/JpbgHKG.png')
                    .setFooter({ 
                        text: 'BuyBoosting.com | Premium Boosting Services', 
                        iconURL: 'https://i.imgur.com/JpbgHKG.png' 
                    })
                    .setTimestamp();

                await interaction.editReply({ 
                    embeds: [fallbackEmbed], 
                    components: [actionRow] 
                });
            }

        } catch (error) {
            console.error('Error executing discountdm command:', error);
            
            // Handle any other errors
            const errorEmbed = new EmbedBuilder()
                .setColor('#ff0000')
                .setTitle('❌ Command Error')
                .setDescription('There was an error while executing this command. Please try again later or contact support.')
                .setFooter({ text: 'BuyBoosting.com' })
                .setTimestamp();

            if (interaction.replied || interaction.deferred) {
                await interaction.editReply({ embeds: [errorEmbed] });
            } else {
                await interaction.reply({ embeds: [errorEmbed], flags: [MessageFlags.Ephemeral] });
            }
        }
    }
};
