// Main bot file: index.js
import dotenv from 'dotenv';
dotenv.config();
import fs from 'node:fs';
import path from 'node:path';
import { fileURLToPath, pathToFileURL } from 'node:url';
import { Client, Collection, GatewayIntentBits, Events, ModalBuilder, TextInputBuilder, TextInputStyle, ActionRowBuilder, MessageFlags, StringSelectMenuBuilder, StringSelectMenuOptionBuilder, PermissionsBitField, EmbedBuilder, ButtonBuilder, ButtonStyle } from 'discord.js';
// Load config using fs.readFileSync for broader compatibility
const __filename_config = fileURLToPath(import.meta.url);
const __dirname_config = path.dirname(__filename_config);
const configPath = path.join(__dirname_config, 'config.json');
const configData = JSON.parse(fs.readFileSync(configPath, 'utf-8'));

import {
    createTicket,
    handleCloseTicketButton,
    logTicketAction, // logTicketAction is also in ticketLogging.js, re-exported by ticketManager/index.js
    claimTicket,
    processTicketClosure, // Renamed from closeTicket
    releaseTicket
} from './ticketManager/index.js'; // Updated path to the barrel file

let config = configData; // Keep config mutable if needed, otherwise could be const

// Override config with environment variables
const envToken = process.env[config.botTokenEnv] || process.env.DISCORD_TOKEN;
const envClientId = process.env[config.clientIdEnv] || process.env.CLIENT_ID;
const envGuildId = process.env[config.guildIdEnv] || process.env.GUILD_ID;
const envSupportRoleIds = process.env[config.supportRoleIdsEnv] || process.env.SUPPORT_ROLE_IDS;
const envLogChannelId = process.env[config.logChannelIdEnv] || process.env.LOG_CHANNEL_ID;
const envTranscriptLogChannelId = process.env[config.transcripts.logChannelIdEnv] || process.env.TRANSCRIPT_LOG_CHANNEL_ID;

if (!envToken) {
    console.error("FATAL: Bot token is not defined. Please set DISCORD_TOKEN in your .env file or the environment variable specified in config.json (botTokenEnv).");
    process.exit(1);
}

config = {
    ...config,
    botToken: envToken,
    clientId: envClientId,
    guildId: envGuildId,
    supportRoleIds: envSupportRoleIds ? envSupportRoleIds.split(',').map(id => id.trim()).filter(id => id) : [],
    logChannelId: envLogChannelId,
    transcripts: {
        ...config.transcripts,
        logChannelId: envTranscriptLogChannelId,
    }
};

// Validate essential configurations after override
if (!config.clientId) {
    console.warn("Warning: Client ID is not defined. Slash command deployment might fail. Set CLIENT_ID in .env or clientIdEnv in config.json.");
}
if (!config.guildId) {
    console.warn("Warning: Guild ID is not defined. Slash command deployment will be global or might fail. Set GUILD_ID in .env or guildIdEnv in config.json for guild-specific commands during development.");
}


const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent, // If you need to read message content (ensure it's enabled in Dev Portal)
        GatewayIntentBits.GuildMembers, // If you need member information
    ]
});

client.commands = new Collection();
client.cooldowns = new Collection(); // For command cooldowns, if needed later

// Load commands
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const commandsPath = path.join(__dirname, 'commands');
const commandFiles = fs.readdirSync(commandsPath).filter(file => file.endsWith('.js'));

// Use an async IIFE to load commands because top-level await is only for modules
(async () => {
    for (const file of commandFiles) {
        const filePath = path.join(commandsPath, file);
        // For ES modules, dynamic import paths should preferably be relative or absolute file URLs.
        // Using new URL() with import.meta.url as base is a robust way.
        const fileUrl = pathToFileURL(filePath).href;
        const commandModule = await import(fileUrl);
        const command = commandModule.default || commandModule; // Handle default or named exports

        // Assuming commands export 'data' and 'execute' as named exports
        // If they are on a default export, it would be command.default.data, etc.
        // Based on the original check, they are direct properties of the imported module object.
        if (command.data && command.execute) {
            client.commands.set(command.data.name, command);
        } else if (commandModule.data && commandModule.execute) { // Check commandModule directly if not using default
            client.commands.set(commandModule.data.name, commandModule);
        }
        else {
            console.log(`[WARNING] The command at ${filePath} is missing a required "data" or "execute" property.`);
        }
    }
    // Ensure config is fully loaded and processed before commands might need it
    // This might involve moving the config loading/override logic if it's not already done
    // or ensuring commands access a globally available, fully processed config object.
})();

// Event handler for when the client is ready
client.once(Events.ClientReady, async readyClient => { // Made async
    console.log(`Ready! Logged in as ${readyClient.user.tag}`);
    // You can set the bot's activity here if you want
    // readyClient.user.setActivity('Managing Tickets', { type: 'WATCHING' });

    // Dynamically set the panel emoji
    if (config.ticketPanel.panelEmojiId) {
        try {
            // Use resolve to get from cache, or fetch if not cached (though fetch might not be needed if bot has GUILD_EMOJIS_AND_STICKERS intent and emoji is from a guild it's in)
            // For simplicity and wider compatibility if emoji is from a guild the bot isn't in but has access via Nitro, resolve is generally fine.
            // However, to be sure it's available to the bot for display, it should be in one of the bot's guilds.
            const emoji = readyClient.emojis.resolve(config.ticketPanel.panelEmojiId);
            if (emoji) {
                config.ticketPanel.embedTitle = config.ticketPanel.embedTitle.replace('{panelEmoji}', emoji.toString());
            } else {
                console.warn(`Could not find custom emoji with ID ${config.ticketPanel.panelEmojiId}. Using default.`);
                config.ticketPanel.embedTitle = config.ticketPanel.embedTitle.replace('{panelEmoji}', config.ticketPanel.defaultPanelEmoji || '📨');
            }
        } catch (error) {
            console.error(`Error fetching custom emoji ${config.ticketPanel.panelEmojiId}:`, error);
            config.ticketPanel.embedTitle = config.ticketPanel.embedTitle.replace('{panelEmoji}', config.ticketPanel.defaultPanelEmoji || '📨');
        }
    } else {
        config.ticketPanel.embedTitle = config.ticketPanel.embedTitle.replace('{panelEmoji}', config.ticketPanel.defaultPanelEmoji || '📨');
    }
});

// Event handler for interactions (slash commands, buttons, etc.)
client.on(Events.InteractionCreate, async interaction => {
    if (interaction.isChatInputCommand()) {
        const command = interaction.client.commands.get(interaction.commandName);

        if (!command) {
            console.error(`No command matching ${interaction.commandName} was found.`);
            await interaction.reply({ content: 'There was an error while executing this command!', flags: MessageFlags.Ephemeral });
            return;
        }

        try {
            await command.execute(interaction, client, config); // Pass client and config to commands
        } catch (error) {
            console.error(error);
            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ content: 'There was an error while executing this command!', flags: MessageFlags.Ephemeral });
            } else {
                await interaction.reply({ content: 'There was an error while executing this command!', flags: MessageFlags.Ephemeral });
            }
        }
    } else if (interaction.isButton()) {
        // Handle button interactions here
        // We'll need to identify buttons by their customId
        // console.log(`Button pressed: ${interaction.customId} by ${interaction.user.tag}`); // Optional: keep for debugging

        if (interaction.customId === config.ticketPanel.buttonCustomId) {
            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId('ticket_type_select')
                .setPlaceholder('Choose a ticket type')
                .addOptions(
                    new StringSelectMenuOptionBuilder()
                        .setLabel('Order issues')
                        .setValue('order_issues')
                        .setDescription('For problems related to your orders.'),
                    new StringSelectMenuOptionBuilder()
                        .setLabel('Purchase Account')
                        .setValue('purchase_account')
                        .setDescription('To inquire about purchasing an account.'),
                    new StringSelectMenuOptionBuilder()
                        .setLabel('Purchase Items')
                        .setValue('purchase_items')
                        .setDescription('To inquire about purchasing in-game items.'),
                );

            const row = new ActionRowBuilder().addComponents(selectMenu);

            await interaction.reply({
                content: 'Please select the reason for opening a ticket:',
                components: [row],
                flags: MessageFlags.Ephemeral,
            });

        } else if (interaction.customId.startsWith(config.ticketMessages.closeButtonCustomId + '_')) {
            // This is the initial "Ticket schließen" button
            await handleCloseTicketButton(interaction, client, config);
        } else if (interaction.customId.startsWith('confirmClose_')) {
            // This is the "Yes, close it" button from the confirmation message
            // We need to define a function in ticketManager.js that this will call, let's name it processTicketClosure
            // For now, let's assume we have processTicketClosure in ticketManager.js
            // const { closeTicket } = require('./ticketManager'); // No longer lazy, imported at top
            await processTicketClosure(interaction, client, config); // Renamed function
        } else if (interaction.customId.startsWith('cancelClose_')) {
            // This is the "Cancel" button from the confirmation message
            try {
                await interaction.update({ content: 'Ticket closure cancelled.', components: [], flags: MessageFlags.Ephemeral });
                await logTicketAction(client, config, `Ticket closure cancelled for channel ${interaction.channel.name} by ${interaction.user.tag}.`);
            } catch (error) {
                console.error('Error cancelling ticket closure:', error);
                // If update fails, it might be due to the message being deleted or interaction timed out
                await logTicketAction(client, config, `ERROR: Failed to update cancel close message for ${interaction.channel.name}. Error: ${error.message}`, true);
            }
        } else if (interaction.customId.startsWith(config.ticketMessages.claimButtonCustomId + '_')) {
            await claimTicket(interaction, client, config);
        } else if (interaction.customId.startsWith(config.ticketMessages.releaseButtonCustomId + '_')) {
            await releaseTicket(interaction, client, config);
        } else if (interaction.customId === 'get_special_offers_role' || interaction.customId === 'remove_special_offers_role') {
            // Delegate to the specialoffers command's button handler
            const command = client.commands.get('specialofferspanel');
            if (command && command.handleButtonInteraction) {
                await command.handleButtonInteraction(interaction, client, config);
            } else {
                console.error(`Could not find handleButtonInteraction for specialofferspanel or command itself.`);
                if (interaction.replied || interaction.deferred) {
                    await interaction.followUp({ content: 'Error processing this action.', flags: MessageFlags.Ephemeral });
                } else {
                    await interaction.reply({ content: 'Error processing this action.', flags: MessageFlags.Ephemeral });
                }
            }
        }
        // Removed the 'support_website_info' button handler as it's now a direct link button
    } else if (interaction.isStringSelectMenu()) {
        if (interaction.customId === 'ticket_type_select') {
            const selectedTicketType = interaction.values[0];

            // Removed 'request_payout' and 'content_creator_application' handlers
            // New handlers for 'purchase_account' and 'purchase_items' will be added below.
            // For now, the 'else' block will catch 'order_issues' and any new types not yet handled.
            if (selectedTicketType === 'order_issues') { // Default to 'order_issues' or any other type
                const modal = new ModalBuilder()
                    .setCustomId(`ticketReasonModal_${selectedTicketType}`)
                    .setTitle(`New Ticket: ${selectedTicketType === 'order_issues' ? 'Order Issue' : 'General Inquiry'}`);

                const subjectInput = new TextInputBuilder()
                    .setCustomId('ticketSubjectInput')
                    .setLabel("Subject / Brief summary")
                    .setStyle(TextInputStyle.Short)
                    .setPlaceholder('e.g., Problem with order #123')
                    .setRequired(true)
                    .setMaxLength(100);

                const reasonInput = new TextInputBuilder()
                    .setCustomId('ticketReasonInput')
                    .setLabel("Please describe your issue/request in detail")
                    .setStyle(TextInputStyle.Paragraph)
                    .setPlaceholder('Provide all necessary details.')
                    .setRequired(true)
                    .setMinLength(10)
                    .setMaxLength(2000);

                const orderIdInput = new TextInputBuilder()
                    .setCustomId('ticketOrderIdInput')
                    .setLabel("Order ID (Numbers Only, or 'N/A')")
                    .setStyle(TextInputStyle.Short)
                    .setPlaceholder("Enter Order ID (e.g., 12345) or N/A")
                    .setRequired(true)
                    .setMaxLength(50);

                const firstActionRow = new ActionRowBuilder().addComponents(subjectInput);
                const secondActionRow = new ActionRowBuilder().addComponents(orderIdInput);
                const thirdActionRow = new ActionRowBuilder().addComponents(reasonInput);

                modal.addComponents(firstActionRow, secondActionRow, thirdActionRow);
                await interaction.showModal(modal);
console.log(`[SelectMenu] State before showModal for '${selectedTicketType}': ID=${interaction.id}, Replied=${interaction.replied}, Deferred=${interaction.deferred}, Updated=${interaction.isMessageComponent() && interaction.message.flags.has(MessageFlags.Ephemeral) ? 'N/A (ephemeral)' : (interaction.isMessageComponent() ? interaction.message.editedTimestamp : 'N/A')}`);
            } else if (selectedTicketType === 'purchase_account') {
                const modal = new ModalBuilder()
                    .setCustomId(`purchaseAccountModal_${selectedTicketType}`)
                    .setTitle('Purchase Account Inquiry');

                const gameInput = new TextInputBuilder()
                    .setCustomId('purchaseAccountGameInput')
                    .setLabel("Which game is the account for?")
                    .setStyle(TextInputStyle.Short)
                    .setPlaceholder('e.g., Valorant, League of Legends')
                    .setRequired(true)
                    .setMaxLength(100);

                const specificsInput = new TextInputBuilder()
                    .setCustomId('purchaseAccountSpecificsInput')
                    .setLabel("Account specifics (level, rank, features)")
                    .setStyle(TextInputStyle.Paragraph)
                    .setPlaceholder('e.g., Level 50, Diamond Rank, specific skins')
                    .setRequired(true)
                    .setMinLength(10)
                    .setMaxLength(1000);

                const firstActionRow = new ActionRowBuilder().addComponents(gameInput);
                const secondActionRow = new ActionRowBuilder().addComponents(specificsInput);

                modal.addComponents(firstActionRow, secondActionRow);
                await interaction.showModal(modal);
console.log(`[SelectMenu] State before showModal for '${selectedTicketType}': ID=${interaction.id}, Replied=${interaction.replied}, Deferred=${interaction.deferred}`);

            } else if (selectedTicketType === 'purchase_items') {
                const modal = new ModalBuilder()
                    .setCustomId(`purchaseItemsModal_${selectedTicketType}`)
                    .setTitle('Purchase Items Inquiry');

                const gameInput = new TextInputBuilder()
                    .setCustomId('purchaseItemsGameInput')
                    .setLabel("Which game are the items for?")
                    .setStyle(TextInputStyle.Short)
                    .setPlaceholder('e.g., CS:GO, Fortnite')
                    .setRequired(true)
                    .setMaxLength(100);

                const itemsInput = new TextInputBuilder()
                    .setCustomId('purchaseItemsItemsInput')
                    .setLabel("Which item(s) are you looking for?")
                    .setStyle(TextInputStyle.Paragraph)
                    .setPlaceholder('e.g., Dragon Lore AWP, 1000 V-Bucks, specific set')
                    .setRequired(true)
                    .setMinLength(5)
                    .setMaxLength(1000);
                
                const budgetInput = new TextInputBuilder()
                    .setCustomId('purchaseItemsBudgetInput')
                    .setLabel("Quantity / Budget (Optional)")
                    .setStyle(TextInputStyle.Short)
                    .setPlaceholder('e.g., 5 units, around $50')
                    .setRequired(false)
                    .setMaxLength(100);

                const firstActionRow = new ActionRowBuilder().addComponents(gameInput);
                const secondActionRow = new ActionRowBuilder().addComponents(itemsInput);
                const thirdActionRow = new ActionRowBuilder().addComponents(budgetInput);

                modal.addComponents(firstActionRow, secondActionRow, thirdActionRow);
                await interaction.showModal(modal);
console.log(`[SelectMenu] State before showModal for '${selectedTicketType}': ID=${interaction.id}, Replied=${interaction.replied}, Deferred=${interaction.deferred}`);
            } else {
                // Fallback for any unknown ticket types from select menu
                console.warn(`Unknown ticket type selected: ${selectedTicketType}`);
                await interaction.reply({ content: 'You selected an unknown ticket type. Please try again or contact support.', flags: MessageFlags.Ephemeral });
            }
        }
    } else if (interaction.isModalSubmit()) {
        if (interaction.customId.startsWith('ticketReasonModal_')) {
            const ticketType = interaction.customId.substring('ticketReasonModal_'.length);
            const ticketSubject = interaction.fields.getTextInputValue('ticketSubjectInput');
            const ticketOrderIdValue = interaction.fields.getTextInputValue('ticketOrderIdInput').trim();
            const ticketReason = interaction.fields.getTextInputValue('ticketReasonInput');

            if (ticketOrderIdValue.toLowerCase() !== 'n/a' && !/^\d+$/.test(ticketOrderIdValue)) {
                await interaction.reply({
                    content: 'Invalid Order ID. Please enter a numeric Order ID or "N/A".',
                    flags: MessageFlags.Ephemeral
                });
                return;
            }
            
            await createTicket(interaction, client, config, ticketSubject, ticketReason, ticketOrderIdValue, ticketType);
        } else if (interaction.customId.startsWith('purchaseAccountModal_')) {
            const ticketType = interaction.customId.substring('purchaseAccountModal_'.length);
            const game = interaction.fields.getTextInputValue('purchaseAccountGameInput');
            const specifics = interaction.fields.getTextInputValue('purchaseAccountSpecificsInput');

            const ticketSubject = `Account Purchase Inquiry: ${game}`;
            const ticketReason = `**Game:** ${game}\n**Account Specifics:**\n${specifics}`;
            const ticketOrderId = "N/A";

            await createTicket(interaction, client, config, ticketSubject, ticketReason, ticketOrderId, ticketType);
        } else if (interaction.customId.startsWith('purchaseItemsModal_')) {
            const ticketType = interaction.customId.substring('purchaseItemsModal_'.length);
            const game = interaction.fields.getTextInputValue('purchaseItemsGameInput');
            const items = interaction.fields.getTextInputValue('purchaseItemsItemsInput');
            const budget = interaction.fields.getTextInputValue('purchaseItemsBudgetInput') || "Not specified";

            const ticketSubject = `Item Purchase Inquiry: ${game}`;
            const ticketReason = `**Game:** ${game}\n**Item(s):**\n${items}\n**Quantity/Budget:** ${budget}`;
            const ticketOrderId = "N/A";

            await createTicket(interaction, client, config, ticketSubject, ticketReason, ticketOrderId, ticketType);
        }
    }
});

// Event handler for when a new member joins the guild
client.on(Events.GuildMemberAdd, async member => {
    if (!config.autoAssignRoleIdOnJoin) {
        // console.log('Auto-assign role ID not configured. Skipping.');
        return;
    }

    const roleIdToAssign = config.autoAssignRoleIdOnJoin;
    const role = member.guild.roles.cache.get(roleIdToAssign);

    if (!role) {
        console.warn(`[GuildMemberAdd] Role with ID ${roleIdToAssign} not found. Cannot assign to ${member.user.tag}.`);
        if (config.logChannelId) {
            try {
                const logChannel = await client.channels.fetch(config.logChannelId);
                if (logChannel && logChannel.isTextBased()) {
                    logChannel.send(`⚠️ **Role Not Found:** Could not assign auto-role to ${member.user.tag} (${member.id}). Role ID ${roleIdToAssign} not found.`);
                }
            } catch (logError) {
                console.error(`[GuildMemberAdd] Error sending 'role not found' log:`, logError);
            }
        }
        return;
    }

    // Check if bot has permissions to manage roles
    const botMember = await member.guild.members.fetchMe();
    if (!botMember.permissions.has(PermissionsBitField.Flags.ManageRoles)) {
        console.warn(`[GuildMemberAdd] Bot does not have Manage Roles permission. Cannot assign role ${role.name} to ${member.user.tag}.`);
        if (config.logChannelId) {
            try {
                const logChannel = await client.channels.fetch(config.logChannelId);
                if (logChannel && logChannel.isTextBased()) {
                    logChannel.send(`🚫 **Permission Missing:** Bot lacks "Manage Roles" permission. Could not assign role "${role.name}" to ${member.user.tag} (${member.id}).`);
                }
            } catch (logError) {
                console.error(`[GuildMemberAdd] Error sending 'permission missing' log:`, logError);
            }
        }
        return;
    }
    
    // Check if bot's highest role is lower than the role to assign (cannot assign roles higher than its own)
    if (botMember.roles.highest.position <= role.position) {
        console.warn(`[GuildMemberAdd] Bot's highest role is not high enough to assign role ${role.name} to ${member.user.tag}.`);
        if (config.logChannelId) {
            try {
                const logChannel = await client.channels.fetch(config.logChannelId);
                if (logChannel && logChannel.isTextBased()) {
                    logChannel.send(`⚠️ **Hierarchy Issue:** Bot's role is not high enough to assign role "${role.name}" to ${member.user.tag} (${member.id}). Please ensure the bot's role is above the auto-assign role.`);
                }
            } catch (logError) {
                console.error(`[GuildMemberAdd] Error sending 'hierarchy issue' log:`, logError);
            }
        }
        return;
    }


    try {
        await member.roles.add(role);
        console.log(`[GuildMemberAdd] Assigned role "${role.name}" to new member ${member.user.tag} (${member.id}).`);
        if (config.logChannelId) {
            try {
                const logChannel = await client.channels.fetch(config.logChannelId);
                if (logChannel && logChannel.isTextBased()) {
                    logChannel.send(`✅ **Role Assigned:** Role "${role.name}" automatically assigned to new member ${member.user.tag} (${member.id}).`);
                }
            } catch (logError) {
                console.error(`[GuildMemberAdd] Error sending 'role assigned' log:`, logError);
            }
        }
    } catch (error) {
        console.error(`[GuildMemberAdd] Failed to assign role ${role.name} to ${member.user.tag}:`, error);
        if (config.logChannelId) {
            try {
                const logChannel = await client.channels.fetch(config.logChannelId);
                if (logChannel && logChannel.isTextBased()) {
                    logChannel.send(`❌ **Role Assign Failed:** Could not assign role "${role.name}" to ${member.user.tag} (${member.id}). Error: ${error.message}`);
                }
            } catch (logError) {
                console.error(`[GuildMemberAdd] Error sending 'role assign failed' log:`, logError);
            }
        }
    }

    // Send Welcome DM if enabled
    if (config.sendWelcomeDmOnJoin) {
        const welcomeSettings = config.welcomeDmSettings || {};
        const rulesChannelId = "1375560324224253952";
        const specialOffersChannelId = "1375892381122957392";
        const supportTicketChannelId = "1375914260231880724";

        const welcomeMessage = `Hello ${member.toString()}, and a warm welcome to the Official BuyBoosting Community Discord!\n\nWe're glad to have you with us. Please take a moment to explore all our channels—especially <#${rulesChannelId}>, <#${specialOffersChannelId}>, and <#${supportTicketChannelId}> if you ever need assistance.\n\n🎉 **Special Welcome Offer!** 🎉\nUse discount code **DISCORD15** for 15% off your next order at BuyBoosting.com!\n\nIf you have any questions, feel free to reach out. We're here to help!\n\nEnjoy your stay and happy boosting!`;

        const welcomeEmbed = new EmbedBuilder()
            .setColor(config.embedColors?.default || '#ffa300')
            .setDescription(welcomeMessage)
            .setImage(welcomeSettings.imageUrl || 'https://i.imgur.com/bUsQB4i.gif')
            .setFooter({ text: welcomeSettings.footerText || 'Welcome to the BuyBoosting Community!' })
            .setTimestamp();

        const visitSiteButton = new ButtonBuilder()
            .setLabel(welcomeSettings.buttonLabel || 'Visit BuyBoosting.com')
            .setURL(welcomeSettings.buttonUrl || 'https://buyboosting.com')
            .setStyle(ButtonStyle.Link);

        const actionRow = new ActionRowBuilder().addComponents(visitSiteButton);

        try {
            await member.send({ embeds: [welcomeEmbed], components: [actionRow] });
            console.log(`[GuildMemberAdd] Sent welcome DM to ${member.user.tag} (${member.id}).`);
            if (config.logChannelId) {
                try {
                    const logChannel = await client.channels.fetch(config.logChannelId);
                    if (logChannel && logChannel.isTextBased()) {
                        logChannel.send(`📨 **Welcome DM Sent:** Successfully sent welcome DM to ${member.user.tag} (${member.id}).`);
                    }
                } catch (logError) {
                    console.error(`[GuildMemberAdd] Error sending 'welcome DM sent' log:`, logError);
                }
            }
        } catch (dmError) {
            console.warn(`[GuildMemberAdd] Could not send welcome DM to ${member.user.tag} (${member.id}). They might have DMs disabled. Error: ${dmError.message}`);
            if (config.logChannelId) {
                try {
                    const logChannel = await client.channels.fetch(config.logChannelId);
                    if (logChannel && logChannel.isTextBased()) {
                        logChannel.send(`⚠️ **Welcome DM Failed:** Could not send welcome DM to ${member.user.tag} (${member.id}). User may have DMs disabled.`);
                    }
                } catch (logError) {
                    console.error(`[GuildMemberAdd] Error sending 'welcome DM failed' log:`, logError);
                }
            }
        }
    }
});

// Log in to Discord with your client's token
client.login(config.botToken);

// Basic error handling
process.on('unhandledRejection', error => {
    console.error('Unhandled promise rejection:', error);
});
process.on('uncaughtException', error => {
    console.error('Uncaught exception:', error);
    // process.exit(1); // Consider if you want to exit on uncaught exceptions
});

console.log('Attempting to log in...');