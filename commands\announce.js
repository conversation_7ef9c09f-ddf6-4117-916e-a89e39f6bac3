import { <PERSON><PERSON><PERSON><PERSON>mandBuilder, Embed<PERSON>uilder, PermissionsBitField, ChannelType, MessageFlags } from 'discord.js';

export default {
    data: new SlashCommandBuilder()
        .setName('announce')
        .setDescription('Sends an announcement to a specified channel or the current channel.')
        .addStringOption(option =>
            option.setName('message')
                .setDescription('The content of the announcement.')
                .setRequired(true))
        .addChannelOption(option =>
            option.setName('channel')
                .setDescription('The channel to send the announcement to. Defaults to current channel.')
                .addChannelTypes(ChannelType.GuildText, ChannelType.GuildAnnouncement) // Allow text and announcement channels
                .setRequired(false))
        .addStringOption(option =>
            option.setName('title')
                .setDescription('An optional title for the announcement embed.')
                .setRequired(false))
        .addRoleOption(option =>
            option.setName('mention_role')
                .setDescription('An optional role to mention with the announcement.')
                .setRequired(false))
        .addStringOption(option =>
            option.setName('image_url')
                .setDescription('An optional image URL to include in the embed.')
                .setRequired(false))
        .setDefaultMemberPermissions(PermissionsBitField.Flags.Administrator)
        .setDMPermission(false),

    async execute(interaction, client, config) {
        try {
            await interaction.deferReply({ ephemeral: true });

            const messageContent = interaction.options.getString('message');
            const targetChannel = interaction.options.getChannel('channel') || interaction.channel;
            const title = interaction.options.getString('title');
            const roleToMention = interaction.options.getRole('mention_role');
            const imageUrl = interaction.options.getString('image_url');

            if (!targetChannel || (targetChannel.type !== ChannelType.GuildText && targetChannel.type !== ChannelType.GuildAnnouncement)) {
                await interaction.editReply({ content: 'Invalid channel selected. Please select a text or announcement channel.', flags: [MessageFlags.Ephemeral] });
                return;
            }

            const announcementEmbed = new EmbedBuilder()
                .setColor(config.embedColors?.default || '#0099ff') // Use global default or a fallback
                .setDescription(messageContent)
                .setTimestamp()
                .setFooter({ text: "Announcement - BuyBoosting.com" }); // Hardcoded footer text

            if (title) {
                announcementEmbed.setTitle(title);
            }

            if (imageUrl) {
                if (imageUrl.match(/\.(jpeg|jpg|gif|png)$/i)) { // Basic URL validation for images
                    announcementEmbed.setImage(imageUrl);
                } else {
                    await interaction.editReply({ content: 'The provided image URL does not appear to be a valid image. Announcement not sent.', flags: [MessageFlags.Ephemeral] });
                    return;
                }
            }

            let messagePayload = { embeds: [announcementEmbed] };
            let mentionContent = "";

            if (roleToMention) {
                // Check if the role is @everyone or @here
                if (roleToMention.id === interaction.guild.id) { // @everyone role ID is the guild ID
                    mentionContent = "@everyone";
                } else if (roleToMention.name.toLowerCase() === '@here') { // Check by name for @here (less reliable, but an option)
                     mentionContent = "@here"; // Note: @here might not work as expected if bot doesn't have perms or if it's not a true @here mention
                }
                else {
                    mentionContent = roleToMention.toString();
                }
                messagePayload.content = mentionContent;
            }
            
            // Check bot permissions for the target channel
            const botPermissions = targetChannel.permissionsFor(client.user);
            if (!botPermissions || !botPermissions.has(PermissionsBitField.Flags.SendMessages) || !botPermissions.has(PermissionsBitField.Flags.EmbedLinks)) {
                await interaction.editReply({ content: `I don't have permissions to send messages or embeds in ${targetChannel}. Please check my permissions.`, flags: [MessageFlags.Ephemeral]});
                return;
            }


            await targetChannel.send(messagePayload);
            await interaction.editReply({ content: `Announcement successfully sent to ${targetChannel}.` });

        } catch (error) {
            console.error('Error executing announce command:', error);
            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ content: 'There was an error while executing this command.', flags: [MessageFlags.Ephemeral] });
            } else {
                await interaction.reply({ content: 'There was an error while executing this command!', flags: [MessageFlags.Ephemeral] });
            }
        }
    }
};