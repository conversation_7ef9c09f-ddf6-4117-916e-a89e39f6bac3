// ticketManager/ticketLogging.js
import fs from 'node:fs'; // Though not directly used in this function, good to keep for consistency if other log methods are added
import path from 'node:path'; // Same as above

/**
 * Logs a ticket-related action to the configured log channel.
 * @param {import('discord.js').Client} client The Discord client instance.
 * @param {object} config The bot's configuration object.
 * @param {string} message The message to log.
 * @param {boolean} [isError=false] Whether the log entry is an error.
 */
export async function logTicketAction(client, config, message, isError = false) {
    if (!config.logChannelId) {
        console.log(`${isError ? '[ERROR_LOG]' : '[LOG]'} (No log channel configured): ${message}`);
        return;
    }
    try {
        const logChannel = await client.channels.fetch(config.logChannelId);
        if (logChannel && logChannel.isTextBased()) {
            const timestamp = new Date().toISOString();
            await logChannel.send(`\`[${timestamp}]\` ${isError ? '⚠️ **ERROR** ⚠️: ' : ''}${message}`);
        } else {
            console.log(`${isError ? '[ERROR_LOG]' : '[LOG]'} (Log channel ${config.logChannelId} not found or not text-based): ${message}`);
        }
    } catch (error) {
        console.error(`Failed to fetch or send to log channel (${config.logChannelId}):`, error);
        console.log(`${isError ? '[ERROR_LOG]' : '[LOG]'} (Log channel send/fetch failed): ${message}`);
    }
}