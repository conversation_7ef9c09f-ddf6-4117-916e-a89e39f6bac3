// ticketManager/ticketEmbeds.js
import { EmbedBuilder } from 'discord.js';

/**
 * Builds the initial embed sent when a ticket is created.
 * @param {import('discord.js').User} user The user who created the ticket.
 * @param {object} config The bot's configuration object.
 * @param {string} formattedTicketNumber The formatted ticket number (e.g., "0001").
 * @param {string} ticketSubject The subject of the ticket.
 * @param {string} ticketReason The detailed reason/description for the ticket.
 * @param {string} ticketOrderId The order ID associated with the ticket.
 * @param {string} ticketType The type of the ticket (e.g., "order_issues", "request_payout").
 * @returns {EmbedBuilder} The constructed embed.
 */
export function buildInitialTicketEmbed(user, config, formattedTicketNumber, ticketSubject, ticketReason, ticketOrderId, ticketType) {
    const initialStatus = config.ticketStatus.new || { text: "New", icon: "🆕" }; // Color will be set globally
    const embedColor = config.embedColors?.default || '#ffa300';
    let embedDescription = ticketReason ? `**Issue Details:**\n${ticketReason.substring(0, 1700)}` : "No specific reason provided.";
    embedDescription += "\n\nYour ticket has been created. A supporter will be with you shortly.";

    return new EmbedBuilder()
        .setColor(embedColor)
        .setTitle(`${initialStatus.icon} Ticket #${formattedTicketNumber} - ${ticketSubject || config.ticketMessages.defaultSubject}`)
        .setDescription(embedDescription)
        .addFields(
            { name: 'Status', value: `${initialStatus.icon} ${initialStatus.text}`, inline: true },
            { name: 'Created At', value: `<t:${Math.floor(Date.now() / 1000)}:F>`, inline: true },
            { name: 'Order ID', value: `📦 ${ticketOrderId || 'N/A'}`, inline: true },
            { name: 'Ticket Type', value: `🏷️ ${ticketType ? ticketType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) : 'General'}`, inline: true },
            { name: 'Opened By', value: `👤 ${user.tag} (${user.toString()})`, inline: false },
            { name: 'Assigned To', value: `➖ ${config.ticketMessages.unassignedUser || 'Unassigned'}`, inline: false }
        )
        .setFooter({ text: config.ticketPanel.footerText || "Support System" })
        .setTimestamp();
}

/**
 * Builds an embed for confirming ticket closure.
 * @param {import('discord.js').User} user The user requesting the closure.
 * @param {object} config The bot's configuration object.
 * @returns {EmbedBuilder} The constructed embed.
 */
export function buildCloseConfirmationEmbed(user, config) {
    const embedColor = config.embedColors?.default || '#ffa300';
    return new EmbedBuilder()
        .setColor(embedColor)
        .setTitle('Confirm Ticket Closure')
        .setDescription(config.ticketMessages.confirmCloseMessage || 'Are you sure you want to close this ticket? This action cannot be undone easily.')
        .setFooter({ text: `Requested by ${user.tag}` });
}

/**
 * Updates an existing ticket embed to reflect a "Closed" status.
 * @param {EmbedBuilder | import('discord.js').APIEmbed} originalEmbedData The original embed data or an EmbedBuilder instance.
 * @param {import('discord.js').User} closerUser The user who closed the ticket.
 * @param {object} config The bot's configuration object.
 * @returns {EmbedBuilder} The updated embed.
 */
export function buildTicketClosedEmbed(originalEmbedData, closerUser, config) {
    const closedStatus = config.ticketStatus.closed || { text: "Closed", icon: "🔒" }; // Color will be set globally
    const embedColor = config.embedColors?.default || '#ffa300';
    const currentTimestamp = Math.floor(Date.now() / 1000);

    const updatedEmbedBuilder = new EmbedBuilder(originalEmbedData) // Handles both APIEmbed and EmbedBuilder
        .setColor(embedColor)
        .setFooter({ text: config.ticketPanel.footerText || "Support System" })
        .setTimestamp(currentTimestamp);

    const fields = updatedEmbedBuilder.data.fields || [];
    const statusFieldIndex = fields.findIndex(f => f.name === 'Status');
    if (statusFieldIndex !== -1) {
        fields[statusFieldIndex] = { name: 'Status', value: `${closedStatus.icon} ${closedStatus.text}`, inline: true };
    } else {
        fields.push({ name: 'Status', value: `${closedStatus.icon} ${closedStatus.text}`, inline: true });
    }
    
    // Remove priority field if it exists
    const priorityFieldIndex = fields.findIndex(f => f.name === 'Priority');
    if (priorityFieldIndex !== -1) {
        fields.splice(priorityFieldIndex, 1);
    }

    // Update or add "Last Updated" field
    const lastUpdatedIndex = fields.findIndex(f => f.name === 'Last Updated');
    if (lastUpdatedIndex !== -1) {
        fields[lastUpdatedIndex] = { name: 'Last Updated', value: `<t:${currentTimestamp}:F>`, inline: false };
    } else {
        // Try to insert it logically, e.g., after 'Opened By' or 'Assigned To'
        let insertAtIndex = fields.findIndex(f => f.name === 'Opened By');
        if (insertAtIndex !== -1) insertAtIndex++;
        else {
            insertAtIndex = fields.findIndex(f => f.name === 'Assigned To');
            if (insertAtIndex !== -1) insertAtIndex++;
            else insertAtIndex = fields.length; // Append if specific fields not found
        }
        fields.splice(insertAtIndex, 0, { name: 'Last Updated', value: `<t:${currentTimestamp}:F>`, inline: false });
    }
    
    updatedEmbedBuilder.setFields(fields); // Set the modified fields array

    if (updatedEmbedBuilder.data.title) {
        const statusIcons = Object.values(config.ticketStatus).map(s => s.icon).filter(icon => icon && icon !== closedStatus.icon);
        let newTitle = updatedEmbedBuilder.data.title;
        statusIcons.forEach(icon => {
            if (newTitle.includes(icon)) {
                newTitle = newTitle.replace(icon, closedStatus.icon);
            }
        });
        updatedEmbedBuilder.setTitle(newTitle);
    }
    return updatedEmbedBuilder;
}

/**
 * Builds an embed to notify that a ticket has been claimed.
 * @param {import('discord.js').User} claimerUser The user who claimed the ticket.
 * @param {object} config The bot's configuration object.
 * @returns {EmbedBuilder} The constructed embed.
 */
export function buildClaimNotificationEmbed(claimerUser, config) {
    const claimMessageText = (config.ticketMessages.ticketClaimedMessage || 'This ticket is now being handled by {claimerMention}.')
        .replace('{claimerMention}', claimerUser.toString());
    const embedColor = config.embedColors?.default || '#ffa300';

    return new EmbedBuilder()
        .setColor(embedColor)
        .setTitle("Ticket Claimed")
        .setDescription(claimMessageText)
        .addFields({ name: 'Claimed by', value: claimerUser.tag, inline: true })
        .setTimestamp();
}

/**
 * Updates the main ticket embed when a ticket is claimed.
 * @param {EmbedBuilder | import('discord.js').APIEmbed} originalEmbedData The original embed data.
 * @param {import('discord.js').User} claimerUser The user who claimed the ticket.
 * @param {object} config The bot's configuration object.
 * @returns {EmbedBuilder} The updated embed.
 */
export function buildTicketClaimedEmbed(originalEmbedData, claimerUser, config) {
    const newStatus = config.ticketStatus.claimed || { text: "Claimed", icon: "🙋" }; // Color will be set globally
    const embedColor = config.embedColors?.default || '#ffa300';
    const currentTimestamp = Math.floor(Date.now() / 1000);

    const updatedEmbedBuilder = new EmbedBuilder(originalEmbedData)
        .setColor(embedColor)
        .setFooter({ text: config.ticketPanel.footerText || "Support System" })
        .setTimestamp(currentTimestamp);

    const fields = updatedEmbedBuilder.data.fields || [];
    const statusFieldIndex = fields.findIndex(f => f.name === 'Status');
    if (statusFieldIndex !== -1) {
        fields[statusFieldIndex] = { name: 'Status', value: `${newStatus.icon} ${newStatus.text}`, inline: true };
    }

    const assignedToIndex = fields.findIndex(f => f.name === 'Assigned To');
    if (assignedToIndex !== -1) {
        fields[assignedToIndex] = { name: 'Assigned To', value: `🛠️ ${claimerUser.tag} (${claimerUser.toString()})`, inline: false };
    } else {
        fields.push({ name: 'Assigned To', value: `🛠️ ${claimerUser.tag} (${claimerUser.toString()})`, inline: false });
    }
    
    const priorityFieldIndex = fields.findIndex(f => f.name === 'Priority');
    if (priorityFieldIndex !== -1) {
        fields.splice(priorityFieldIndex, 1);
    }

    const lastUpdatedIndex = fields.findIndex(f => f.name === 'Last Updated');
    if (lastUpdatedIndex !== -1) {
        fields[lastUpdatedIndex] = { name: 'Last Updated', value: `<t:${currentTimestamp}:F>`, inline: false };
    } else {
        let insertAtIndex = fields.findIndex(f => f.name === 'Opened By');
        if (insertAtIndex !== -1) insertAtIndex++; else insertAtIndex = fields.length;
        fields.splice(insertAtIndex, 0, { name: 'Last Updated', value: `<t:${currentTimestamp}:F>`, inline: false });
    }
    updatedEmbedBuilder.setFields(fields);

    if (updatedEmbedBuilder.data.title) {
        const newStatusIcon = config.ticketStatus.new?.icon || '🆕';
        const openStatusIcon = config.ticketStatus.open?.icon || '🔵';
        let newTitle = updatedEmbedBuilder.data.title;
        if (newTitle.includes(newStatusIcon)) newTitle = newTitle.replace(newStatusIcon, newStatus.icon);
        else if (newTitle.includes(openStatusIcon)) newTitle = newTitle.replace(openStatusIcon, newStatus.icon);
        updatedEmbedBuilder.setTitle(newTitle);
    }
    return updatedEmbedBuilder;
}

/**
 * Builds an embed to notify that a ticket has been released.
 * @param {import('discord.js').User} releaserUser The user who released the ticket.
 * @param {object} config The bot's configuration object.
 * @returns {EmbedBuilder} The constructed embed.
 */
export function buildReleaseNotificationEmbed(releaserUser, config) {
    const releaseMessageText = (config.ticketMessages.ticketReleasedMessage || 'This ticket has been released by {releaserMention} and is now available for all support staff.')
        .replace('{releaserMention}', releaserUser.toString());
    const embedColor = config.embedColors?.default || '#ffa300';

    return new EmbedBuilder()
        .setColor(embedColor)
        .setTitle("Ticket Released")
        .setDescription(releaseMessageText)
        .addFields({ name: 'Released by', value: releaserUser.tag, inline: true })
        .setTimestamp();
}

/**
 * Updates the main ticket embed when a ticket is released.
 * @param {EmbedBuilder | import('discord.js').APIEmbed} originalEmbedData The original embed data.
 * @param {object} config The bot's configuration object.
 * @returns {EmbedBuilder} The updated embed.
 */
export function buildTicketReleasedEmbed(originalEmbedData, config) {
    const newStatus = config.ticketStatus.open || { text: "Open", icon: "🔵" }; // Color will be set globally
    const embedColor = config.embedColors?.default || '#ffa300';
    const currentTimestamp = Math.floor(Date.now() / 1000);

    const updatedEmbedBuilder = new EmbedBuilder(originalEmbedData)
        .setColor(embedColor)
        .setFooter({ text: config.ticketPanel.footerText || "Support System" })
        .setTimestamp(currentTimestamp);

    const fields = updatedEmbedBuilder.data.fields || [];
    const statusFieldIndex = fields.findIndex(f => f.name === 'Status');
    if (statusFieldIndex !== -1) {
        fields[statusFieldIndex] = { name: 'Status', value: `${newStatus.icon} ${newStatus.text}`, inline: true };
    }

    const assignedToIndex = fields.findIndex(f => f.name === 'Assigned To');
    if (assignedToIndex !== -1) {
        fields[assignedToIndex] = { name: 'Assigned To', value: `➖ ${config.ticketMessages.unassignedUser || 'Unassigned'}`, inline: false };
    }
    
    const priorityFieldIndex = fields.findIndex(f => f.name === 'Priority');
    if (priorityFieldIndex !== -1) {
        fields.splice(priorityFieldIndex, 1);
    }

    const lastUpdatedIndex = fields.findIndex(f => f.name === 'Last Updated');
    if (lastUpdatedIndex !== -1) {
        fields[lastUpdatedIndex] = { name: 'Last Updated', value: `<t:${currentTimestamp}:F>`, inline: false };
    } else {
        let insertAtIndex = fields.findIndex(f => f.name === 'Opened By');
        if (insertAtIndex !== -1) insertAtIndex++; else insertAtIndex = fields.length;
        fields.splice(insertAtIndex, 0, { name: 'Last Updated', value: `<t:${currentTimestamp}:F>`, inline: false });
    }
    updatedEmbedBuilder.setFields(fields);

    if (updatedEmbedBuilder.data.title) {
        const claimedStatusIcon = config.ticketStatus.claimed?.icon || '🙋';
        let newTitle = updatedEmbedBuilder.data.title;
        if (newTitle.includes(claimedStatusIcon)) newTitle = newTitle.replace(claimedStatusIcon, newStatus.icon);
        updatedEmbedBuilder.setTitle(newTitle);
    }
    return updatedEmbedBuilder;
}
/**
 * Builds an embed for the community rules.
 * @param {object} config The bot's configuration object.
 * @returns {EmbedBuilder} The constructed embed.
 */
export function buildRulesEmbed(config) {
    const embedColor = config.embedColors?.default || '#ffa300'; // Use global default
    const footerText = config.serverName || "Community Server";

    return new EmbedBuilder()
        .setColor(embedColor)
        .setTitle('📜 Community Rules & Guidelines')
        .setDescription('Welcome! Please read and respect our server rules to ensure a safe and fair environment for everyone. Failure to comply may result in disciplinary actions.')
        .addFields(
            { name: '🚫 General Conduct', value: '1. **Respect All Members:** Treat everyone with respect. Harassment, hate speech, discrimination, or any form of bullying will not be tolerated.\n2. **No Spamming:** Avoid excessive messages, images, or mentions. This includes unsolicited DMs to members.\n3. **No NSFW Content:** Keep all discussions and shared content SFW (Safe For Work). No explicit, gory, or offensive material.\n4. **English Only (Main Channels):** To ensure clear communication and moderation, please use English in public channels unless specified otherwise.\n5. **Follow Discord ToS:** All activities must comply with Discord\'s Terms of Service and Community Guidelines.' },
            { name: '💰 Trading & Selling: General', value: '1. **Clear & Honest Listings:** All sale/trade posts must be clear, accurate, and truthful. Include all relevant details (e.g., account region, rank, items, boosting specifics, price).\n2. **No Scamming:** Scamming will result in an immediate permanent ban and community alert. We have zero tolerance for fraudulent activities.' },
            { name: '🧾 Proof & Dispute Resolution', value: '1. **Proof of Ownership/Service:** Be prepared to provide proof of account/item ownership or previous boosting service completion if requested by staff.\n2. **Dispute Resolution:** In case of a dispute, provide all evidence to server staff. We will investigate and mediate to the best of our ability, but the server is not liable for losses incurred from private deals.'},
            // Removed '🎮 Boosting Ethics & Conduct' field as per request
            { name: '⛔ Prohibited Activities', value: '1. **Illegal Activities:** No discussion or promotion of illegal activities, including but not limited to hacking, cracking, or distributing malicious software.\n2. **Selling Stolen/Cracked Goods:** Selling or trading accounts/items obtained through illicit means (e.g., hacking, phishing) is strictly forbidden.\n3. **Doxing & Privacy Violation:** Sharing personal information of others without their explicit consent (doxing) is strictly prohibited.\n4. **Impersonation:** Do not impersonate staff members, other users, or official entities.\n5. **Exploiting Loopholes:** Attempting to find and exploit loopholes in these rules or server systems is forbidden.' },
            { name: '⚖️ Enforcement & Consequences', value: 'Violation of these rules may lead to:\n- Verbal/Written Warning\n- Temporary Mute or Channel Restriction\n- Temporary Kick from the server\n- Permanent Ban from the server\n\nDecisions are made at the discretion of the server staff. All disciplinary actions are logged.' },
            { name: '📢 Announcements & Rule Updates', value: 'Stay updated by checking the announcements channel. Rules may be updated as needed, and it is your responsibility to be aware of the current guidelines.' }
        )
        .setFooter({ text: `${footerText} - Rules are subject to change. Last Updated: ${new Date().toLocaleDateString()}` })
        .setTimestamp();
}