// ticketManager/index.js
// This file acts as a barrel file for the ticketManager modules.

export * from './ticketLogging.js';
export * from './ticketChannels.js';
export * from './ticketEmbeds.js';
export * from './transcriptService.js';
export * from './ticketActions.js';
export * from './ticketLifecycle.js';

// Also re-export sanitizeUsernameForChannel as it was previously directly available from ticketManager.js
// and might be expected by other files if they weren't updated to import from ticketChannels.js
// However, upon review, sanitizeUsernameForChannel is only used internally within ticketLifecycle.js (via createTicket)
// and ticketChannels.js itself. So, it does not need to be re-exported here for external use.
// The individual modules handle their own internal dependencies.