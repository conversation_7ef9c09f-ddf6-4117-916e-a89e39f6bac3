// Script to deploy slash commands: deploy-commands.js
import dotenv from 'dotenv';
dotenv.config();
import { REST, Routes } from 'discord.js';
import fs from 'node:fs';
import path from 'node:path';
import { fileURLToPath, pathToFileURL } from 'node:url'; // Added pathToFileURL

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const configPath = path.join(__dirname, 'config.json');
let config = JSON.parse(fs.readFileSync(configPath, 'utf-8')); // Load base config

// Override config with environment variables
const envToken = process.env[config.botTokenEnv] || process.env.DISCORD_TOKEN;
const envClientId = process.env[config.clientIdEnv] || process.env.CLIENT_ID;
const envGuildId = process.env[config.guildIdEnv] || process.env.GUILD_ID;

if (!envToken) {
    console.error("FATAL: Bot token is not defined for command deployment. Please set DISCORD_TOKEN in your .env file or the environment variable specified in config.json (botTokenEnv).");
    process.exit(1);
}
if (!envClientId) {
    console.error("FATAL: Client ID is not defined for command deployment. Please set CLIENT_ID in your .env file or the environment variable specified in config.json (clientIdEnv).");
    process.exit(1);
}
// Guild ID is not strictly fatal for deployment (can deploy globally), but highly recommended for testing.
if (!envGuildId) {
    console.warn("Warning: Guild ID is not defined for command deployment. Commands will be deployed globally. For testing, it's recommended to set GUILD_ID in .env or guildIdEnv in config.json and deploy to a specific guild.");
}

config = {
    ...config,
    botToken: envToken,
    clientId: envClientId,
    guildId: envGuildId,
    // Other config values like supportRoleIds, logChannelId are not needed for command deployment script
};

const commands = [];
// Grab all the command files from the commands directory you created earlier
const commandsPath = path.join(__dirname, 'commands');
const commandFiles = fs.readdirSync(commandsPath).filter(file => file.endsWith('.js'));

// Grab the SlashCommandBuilder#toJSON() output of each command's data for deployment
// Grab the SlashCommandBuilder#toJSON() output of each command's data for deployment
// Use an async loop for dynamic imports
(async () => {
    for (const file of commandFiles) {
        const filePath = path.join(commandsPath, file);
        try {
            const fileUrl = pathToFileURL(filePath).href;
            const commandModule = await import(fileUrl);
            const command = commandModule.default || commandModule; // Handle default or named exports

            if (command.data && typeof command.data.toJSON === 'function') {
                commands.push(command.data.toJSON());
            } else if (commandModule.data && typeof commandModule.data.toJSON === 'function') { // Check commandModule directly
                 commands.push(commandModule.data.toJSON());
            }
            else {
                console.log(`[WARNING] The command at ${filePath} is missing a required "data" property or "data.toJSON" function.`);
            }
        } catch (e) {
            console.error(`Error loading command at ${filePath}:`, e);
        }
    }

    // Construct and prepare an instance of the REST module
    const rest = new REST().setToken(config.botToken);

    // and deploy your commands!
    try {
        console.log(`Started refreshing ${commands.length} application (/) commands.`);

        let route;
        if (config.guildId) {
            console.log(`Deploying commands to guild: ${config.guildId}`);
            route = Routes.applicationGuildCommands(config.clientId, config.guildId);
        } else {
            console.log('Deploying commands globally. This might take up to an hour to propagate.');
            route = Routes.applicationCommands(config.clientId);
        }

        const data = await rest.put(
            route,
            { body: commands },
        );

        console.log(`Successfully reloaded ${data.length} application (/) commands ${config.guildId ? `for guild ${config.guildId}` : 'globally'}.`);
    } catch (error) {
        console.error(error);
    }
})();

// The deployment logic has been moved inside the async IIFE that loads commands
// to ensure `commands` array is populated before deployment.