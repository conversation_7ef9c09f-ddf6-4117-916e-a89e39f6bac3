import { SlashCommandBuilder, MessageFlags, PermissionsBitField, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } from 'discord.js';

export default {
    data: new SlashCommandBuilder()
        .setName('clashdm')
        .setDescription('Sends the LoL Clash promotion DM to all members of a specified role.')
        .addRoleOption(option =>
            option.setName('role')
                .setDescription('The role to send a message to')
                .setRequired(true))
        .setDefaultMemberPermissions(0)
        .setDMPermission(false),

    async execute(interaction, client, config) {
        const targetRole = interaction.options.getRole('role');

        const isAdmin = interaction.member.permissions.has(PermissionsBitField.Flags.Administrator);
        const memberHasSupportRole = config.supportRoleIds && config.supportRoleIds.length > 0 && interaction.member.roles.cache.some(role => config.supportRoleIds.includes(role.id));

        if (!isAdmin && !memberHasSupportRole) {
            await interaction.reply({ content: 'You do not have permission to use this command. You need to be an Administrator or have a designated support role.', flags: [MessageFlags.Ephemeral] });
            return;
        }

        if (!targetRole) {
            await interaction.reply({ content: 'You must specify a role.', flags: [MessageFlags.Ephemeral] });
            return;
        }

        try {
            await interaction.deferReply({ flags: [MessageFlags.Ephemeral] });

            await interaction.guild.members.fetch();
            const membersWithRole = interaction.guild.members.cache.filter(member => member.roles.cache.has(targetRole.id) && !member.user.bot);

            if (membersWithRole.size === 0) {
                await interaction.editReply({ content: `No non-bot members found in the role ${targetRole.name}.` });
                return;
            }

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setLabel('Website')
                        .setStyle(ButtonStyle.Link)
                        .setURL('https://buyboosting.com'),
                    new ButtonBuilder()
                        .setLabel('TikTok')
                        .setStyle(ButtonStyle.Link)
                        .setURL('https://www.tiktok.com/@buyboosting')
                );

            let successCount = 0;
            let failCount = 0;

            const sendPromises = membersWithRole.map(member => {
                const messageContent = `📢 LoL Clash Service Now Available!

Hey ${member}!
We're now offering a League of Legends Clash boosting service! If you're interested in playing Clash but need some extra support, we've got you covered.
This service is not implemented in our website yet. Just contact one of our admins through our live chat or open a ticket in <#1375914260231880724>. We will guide you on how to order the Clash service.

✅ Up to 5 boosters per team available
✅ All tiers supported - from Tier IV to Tier I
✅ Up to 20% cheaper than other websites

Just send us a message if you're interested – let’s dominate Clash together! 💪`;

                const embed = new EmbedBuilder()
                    .setColor(config.embedColors?.default || '#ffa300')
                    .setDescription(messageContent)
                    .setImage('https://imgur.com/1AgEqO0.png')
                    .setFooter({ text: 'BuyBoosting.com' });

                return member.send({ embeds: [embed], components: [row] })
                    .then(() => { successCount++; })
                    .catch(error => {
                        console.error(`Failed to send DM to ${member.user.tag} (ID: ${member.id}):`, error.message);
                        failCount++;
                    });
            });

            await Promise.allSettled(sendPromises);

            await interaction.editReply({
                content: `Message sending complete for role ${targetRole.name}.\nSuccessfully sent to: ${successCount} members.\nFailed to send to: ${failCount} members.`,
            });

        } catch (error) {
            console.error('Error executing tiktokdm command:', error);
            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ content: 'There was an error while executing this command.', flags: [MessageFlags.Ephemeral] });
            } else {
                await interaction.reply({ content: 'There was an error while executing this command!', flags: [MessageFlags.Ephemeral] });
            }
        }
    }
};