// utils.js
import fs from 'node:fs';
import path from 'node:path';
import { fileURLToPath } from 'node:url';

// Recreate __dirname for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Gets the next ticket number for a given guild and increments it.
 * Numbers are stored in a JSON file specified in config.
 * @param {string} guildId The ID of the guild.
 * @param {string} persistenceFilePath Path to the JSON file for storing ticket counts.
 * @returns {Promise<number>} The next ticket number.
 */
export async function getNextTicketNumber(guildId, persistenceFilePath) {
    const filePath = path.resolve(__dirname, persistenceFilePath);
    let counts = {};

    try {
        if (fs.existsSync(filePath)) {
            const data = await fs.promises.readFile(filePath, 'utf8');
            counts = JSON.parse(data);
        }
    } catch (error) {
        console.error(`Error reading ticket count file (${filePath}):`, error);
        // If file is corrupted or unreadable, start fresh but log the error
        counts = {};
    }

    counts[guildId] = (counts[guildId] || 0) + 1;

    try {
        await fs.promises.writeFile(filePath, JSON.stringify(counts, null, 2));
    } catch (error) {
        console.error(`Error writing ticket count file (${filePath}):`, error);
        // If writing fails, the number might not be persisted, but we still return it.
        // Consider more robust error handling here for production (e.g., retry, backup)
    }

    return counts[guildId];
}

/**
 * Formats a ticket number with leading zeros.
 * @param {number} number The ticket number.
 * @param {number} length The desired length of the formatted string (e.g., 4 for 0001).
 * @returns {string} The formatted ticket number.
 */
export function formatTicketNumber(number, length = 4) {
    return String(number).padStart(length, '0');
}

/**
 * Loads the bot's configuration from config.json.
 * It also handles overriding with environment variables for critical settings.
 * @returns {Promise<object>} The configuration object.
 */
export async function loadConfig() {
    const configPath = path.resolve(__dirname, 'config.json');
    let config = {};

    try {
        if (fs.existsSync(configPath)) {
            const data = await fs.promises.readFile(configPath, 'utf8');
            config = JSON.parse(data);
        } else {
            console.error(`FATAL: Configuration file (config.json) not found at ${configPath}`);
            process.exit(1); // Exit if config is missing
        }
    } catch (error) {
        console.error(`Error reading or parsing config file (${configPath}):`, error);
        process.exit(1); // Exit on error
    }

    // Override with environment variables (similar to index.js and deploy-commands.js)
    // This ensures consistency in how config is loaded across the application.
    const envToken = process.env[config.botTokenEnv] || process.env.DISCORD_TOKEN;
    const envClientId = process.env[config.clientIdEnv] || process.env.CLIENT_ID;
    const envGuildId = process.env[config.guildIdEnv] || process.env.GUILD_ID;
    const envSupportRoleIds = process.env[config.supportRoleIdsEnv] || process.env.SUPPORT_ROLE_IDS;
    const envLogChannelId = process.env[config.logChannelIdEnv] || process.env.LOG_CHANNEL_ID;
    const envTranscriptLogChannelId = process.env[config.transcripts?.logChannelIdEnv] || process.env.TRANSCRIPT_LOG_CHANNEL_ID; // Added optional chaining for transcripts

    if (!envToken && !config.botToken) { // Check if token is present either in env or already in config (e.g. if not using env override)
        console.error("FATAL: Bot token is not defined. Please set DISCORD_TOKEN in your .env file or the environment variable specified in config.json (botTokenEnv), or directly in config.json.");
        process.exit(1);
    }

    return {
        ...config,
        botToken: envToken || config.botToken, // Prioritize env var
        clientId: envClientId || config.clientId,
        guildId: envGuildId || config.guildId,
        supportRoleIds: envSupportRoleIds ? envSupportRoleIds.split(',').map(id => id.trim()).filter(id => id) : (config.supportRoleIds || []),
        logChannelId: envLogChannelId || config.logChannelId,
        transcripts: {
            ...(config.transcripts || {}), // Ensure transcripts object exists
            logChannelId: envTranscriptLogChannelId || config.transcripts?.logChannelId,
        },
        // Ensure other critical parts of config are present or have defaults
        ticketPanel: config.ticketPanel || {},
        ticketCategories: config.ticketCategories || {},
        ticketMessages: config.ticketMessages || {},
        ticketStatus: config.ticketStatus || {},
        embedColors: {
            default: '#ffa300', // Set the new global default color
            ...(config.embedColors || {}), // Allow overrides from config.json
        },
    };
}

// All functions are now individually exported.
// module.exports is no longer used.