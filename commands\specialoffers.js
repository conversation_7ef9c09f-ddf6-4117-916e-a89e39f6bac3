import { <PERSON><PERSON><PERSON>ommandBuilder, <PERSON>bed<PERSON>uilder, ButtonBuilder, ActionRowBuilder, ButtonStyle, PermissionsBitField, MessageFlags } from 'discord.js';
import { loadConfig } from '../utils.js'; // To load specialOffersRoleId from config

export default {
    data: new SlashCommandBuilder()
        .setName('specialofferspanel')
        .setDescription('Sends an embed with buttons to get/remove the Special Offers role.')
        .setDefaultMemberPermissions(PermissionsBitField.Flags.Administrator)
        .setDMPermission(false),

    async execute(interaction, client, config) { // Added client and config parameters
        try {
            // No need to check permissions here, as setDefaultMemberPermissions handles it for slash commands.
            // Deferring reply for the command itself, as it just posts a message.
            await interaction.deferReply({ ephemeral: true });

            const panelConfig = config.specialOffersPanel || {}; // Use an empty object as fallback

            const panelEmbed = new EmbedBuilder()
                .setColor(config.embedColors?.default || '#4E5D94') // More neutral/formal color if no default
                .setTitle(panelConfig.title || 'Notification Role Management') // Formal title
                .setDescription(panelConfig.description || 'Please use the buttons below to subscribe to or unsubscribe from Special Offer notifications.')
                .addFields(
                    {
                        name: panelConfig.fieldName || 'Role Benefits',
                        value: panelConfig.fieldValue || 'Subscribers receive notifications about exclusive discounts, promotions, and early access to new services.'
                    }
                )
                .setFooter({ text: panelConfig.footerText || `${interaction.guild.name} | Role Management` })
                .setTimestamp();

            if (panelConfig.thumbnailUrl && panelConfig.thumbnailUrl.startsWith('http')) {
                panelEmbed.setThumbnail(panelConfig.thumbnailUrl);
            } else if (panelConfig.thumbnailUrl === null || panelConfig.thumbnailUrl === "") {
                // Explicitly do nothing if thumbnailUrl is null or empty (removes thumbnail)
            } else if (config.specialOffersPanel?.thumbnailUrl) {
                // Fallback for older config structure if any, or default if not explicitly removed
                panelEmbed.setThumbnail(config.specialOffersPanel.thumbnailUrl);
            }


            const getRoleButton = new ButtonBuilder()
                .setCustomId('get_special_offers_role')
                .setLabel(panelConfig.getRoleButtonLabel || 'Subscribe to Special Offers')
                .setStyle(ButtonStyle.Primary);

            if (panelConfig.getRoleButtonEmoji) {
                getRoleButton.setEmoji(panelConfig.getRoleButtonEmoji);
            }

            const removeRoleButton = new ButtonBuilder()
                .setCustomId('remove_special_offers_role')
                .setLabel(panelConfig.removeRoleButtonLabel || 'Unsubscribe from Special Offers')
                .setStyle(ButtonStyle.Secondary); // Changed to Secondary for a less "urgent" look

            if (panelConfig.removeRoleButtonEmoji) {
                removeRoleButton.setEmoji(panelConfig.removeRoleButtonEmoji);
            }

            const row = new ActionRowBuilder().addComponents(getRoleButton, removeRoleButton);

            await interaction.channel.send({ embeds: [panelEmbed], components: [row] });
            await interaction.editReply({ content: 'Special Offers panel successfully posted!' });

        } catch (error) {
            console.error('Error executing specialofferspanel command:', error);
            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ content: 'There was an error while executing this command!', flags: [MessageFlags.Ephemeral] });
            } else {
                await interaction.reply({ content: 'There was an error while executing this command!', flags: [MessageFlags.Ephemeral] });
            }
        }
    },

    async handleButtonInteraction(interaction, client, config) { // Added client and config
        try {
            await interaction.deferReply({ flags: [MessageFlags.Ephemeral] });

            const roleId = config.specialOffersRoleId; // Get from loaded config
            if (!roleId) {
                console.error('specialOffersRoleId is not defined in the configuration.');
                await interaction.editReply({ content: 'Configuration error: Special Offers Role ID is missing.', components: [] });
                return;
            }

            const role = interaction.guild.roles.cache.get(roleId);
            if (!role) {
                console.error(`Role with ID ${roleId} not found in guild ${interaction.guild.name}.`);
                await interaction.editReply({ content: 'Configuration error: Special Offers Role not found.', components: [] });
                return;
            }

            const member = interaction.member;
            const hasRole = member.roles.cache.has(roleId);
            let responseMessage = '';

            if (interaction.customId === 'get_special_offers_role') {
                if (!hasRole) {
                    await member.roles.add(role);
                    responseMessage = `You have successfully received the role "${role.name}"!`;
                } else {
                    responseMessage = `You already have the role "${role.name}".`;
                }
            } else if (interaction.customId === 'remove_special_offers_role') {
                if (hasRole) {
                    await member.roles.remove(role);
                    responseMessage = `The role "${role.name}" has been removed.`;
                } else {
                    responseMessage = `You don't have the role "${role.name}".`;
                }
            } else {
                 console.warn(`Unexpected customId in specialofferspanel button handler: ${interaction.customId}`);
                 responseMessage = 'An unexpected interaction occurred.';
            }

            await interaction.editReply({ content: responseMessage, components: [] });

        } catch (error) {
            console.error('Error handling button interaction in specialofferspanel:', error);
            if (!interaction.replied && interaction.deferred) {
                await interaction.editReply({ content: 'There was an error processing your request. Please try again later.', components: [] });
            } else {
                console.error('Interaction was already replied to or not deferred when error occurred in specialofferspanel handleButtonInteraction.');
                 try {
                     await interaction.followUp({ content: 'There was an error processing your request. Please try again later.', flags: [MessageFlags.Ephemeral] });
                 } catch (followUpError) {
                     console.error('Could not send error follow-up message in specialofferspanel:', followUpError);
                 }
            }
        }
    }
};