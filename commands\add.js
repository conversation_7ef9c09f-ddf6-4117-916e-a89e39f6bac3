// commands/add.js
import { <PERSON>lash<PERSON><PERSON>mandBuilder, PermissionsBitField, ChannelType, EmbedBuilder, MessageFlags } from 'discord.js';
import { logTicketAction } from '../ticketManager/index.js'; // Explicitly point to the barrel file

export const data = new SlashCommandBuilder()
    .setName('add')
    .setDescription('Adds a user to the current ticket channel.')
    .addUserOption(option =>
        option.setName('user')
            .setDescription('The user to add to this ticket channel.')
            .setRequired(true))
    .setDefaultMemberPermissions(PermissionsBitField.Flags.Administrator)
    .setDMPermission(false);

export async function execute(interaction, client, config) {
        const targetUser = interaction.options.getUser('user');
        const channel = interaction.channel;
        const member = interaction.member; // Member who executed the command

        if (!member.permissions.has(PermissionsBitField.Flags.Administrator)) {
            return interaction.reply({ content: 'You do not have permission to use this command.', flags: MessageFlags.Ephemeral });
        }

        // Check if the command is used in a valid ticket channel
        // A more robust check would be to see if it's in the configured ticket category
        const ticketCategory = interaction.guild.channels.cache.find(
            c => c.name === config.ticketCategoryName && c.type === ChannelType.GuildCategory
        );

        const validPrefixes = ['ticket-', 'order-issue-', 'payout-request-'];
        const hasValidPrefix = validPrefixes.some(prefix => channel.name.startsWith(prefix));

        if (!hasValidPrefix || (ticketCategory && channel.parentId !== ticketCategory.id)) {
            return interaction.reply({ content: 'This command can only be used in a ticket channel.', flags: MessageFlags.Ephemeral });
        }

        if (!targetUser) {
            return interaction.reply({ content: 'Could not find the specified user.', flags: MessageFlags.Ephemeral });
        }

        try {
            await channel.permissionOverwrites.edit(targetUser.id, {
                ViewChannel: true,
                SendMessages: true,
                ReadMessageHistory: true,
                AttachFiles: true,
                EmbedLinks: true,
            });

            const addEmbed = new EmbedBuilder()
                .setColor(config.ticketPanel.embedColor || '#00FF00') // Green for success, or use panel color
                .setTitle('User Added to Ticket')
                .setDescription(`${targetUser.toString()} has been added to this ticket.`)
                .addFields({ name: 'Added by', value: member.toString(), inline: true })
                .setTimestamp();

            await channel.send({ embeds: [addEmbed] });
            // The interaction reply should be ephemeral if the main confirmation is in the channel
            await interaction.reply({ content: `${targetUser.tag} has been successfully added to this ticket. A notification has been posted in the channel.`, flags: MessageFlags.Ephemeral });
            await logTicketAction(client, config, `${member.user.tag} added ${targetUser.tag} to ticket ${channel.name} (${channel.id}).`);

        } catch (error) {
            console.error(`Error adding user ${targetUser.tag} to ticket ${channel.name}:`, error);
            await logTicketAction(client, config, `ERROR: Failed to add ${targetUser.tag} to ticket ${channel.name} by ${member.user.tag}. Error: ${error.message}`, true);
            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ content: 'There was an error trying to add the user to this ticket.', flags: MessageFlags.Ephemeral });
            } else {
                await interaction.reply({ content: 'There was an error trying to add the user to this ticket.', flags: MessageFlags.Ephemeral });
            }
        }
} // Added back the closing brace for the execute function