import { SlashCommandBuilder, PermissionFlagsBits, ChannelType } from 'discord.js';
import { buildRulesEmbed } from '../ticketManager/ticketEmbeds.js';
import { loadConfig } from '../utils.js'; // Assuming you have a way to load config

export default {
    data: new SlashCommandBuilder()
        .setName('sendrules')
        .setDescription('Sends the server rules embed to the specified channel or current channel.')
        .addChannelOption(option =>
            option.setName('channel')
                .setDescription('The channel to send the rules embed to.')
                .addChannelTypes(ChannelType.GuildText) // Ensure it's a text channel
                .setRequired(false)) // Make it optional, defaults to current channel
        .setDefaultMemberPermissions(PermissionFlagsBits.Administrator) // Only admins can use it
        .setDMPermission(false), // Not available in DMs

    async execute(interaction) {
        const config = await loadConfig(); // Load configuration
        const targetChannel = interaction.options.getChannel('channel') || interaction.channel;

        if (!targetChannel || targetChannel.type !== ChannelType.GuildText) {
            return interaction.reply({ content: 'Please specify a valid text channel or run this command in one.', ephemeral: true });
        }

        try {
            const rulesEmbed = buildRulesEmbed(config);
            await targetChannel.send({ embeds: [rulesEmbed] });

            // Ephemeral confirmation to the command user
            await interaction.reply({ content: `Rules embed has been sent to ${targetChannel}.`, ephemeral: true });

            // If the command was used in a different channel than the target, also send a non-ephemeral message there.
            if (interaction.channel.id !== targetChannel.id) {
                 await interaction.followUp({ content: `Rules embed also sent to ${targetChannel} by ${interaction.user}.` });
            }

        } catch (error) {
            console.error('Error sending rules embed:', error);
            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ content: 'There was an error trying to send the rules embed.', ephemeral: true });
            } else {
                await interaction.reply({ content: 'There was an error trying to send the rules embed.', ephemeral: true });
            }
        }
    },
};