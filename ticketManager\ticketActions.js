// ticketManager/ticketActions.js
import { PermissionsBitField, ButtonBuilder, ButtonStyle, ActionRowBuilder, MessageFlags } from 'discord.js';
import { logTicketAction } from './ticketLogging.js';
import { updateTicketChannelTopic } from './ticketChannels.js';
import { buildClaimNotificationEmbed, buildTicketClaimedEmbed, buildReleaseNotificationEmbed, buildTicketReleasedEmbed } from './ticketEmbeds.js';

export async function claimTicket(interaction, client, config) {
    const guild = interaction.guild;
    const user = interaction.user; 
    const channel = interaction.channel;
    const parts = interaction.customId.split('_');
    if (parts.length < 3) {
        console.error(`Invalid customId for claim button: ${interaction.customId}`);
        await interaction.reply({ content: 'Error processing this action. Invalid button ID.', flags: MessageFlags.Ephemeral });
        return;
    }
    const ticketChannelId = parts[1];
    // const ticketCreatorId = parts[2]; // Not strictly needed for claim logic itself but good for context

    if (channel.id !== ticketChannelId) {
        await interaction.reply({ content: "This button seems to be for a different ticket channel.", flags: MessageFlags.Ephemeral });
        return;
    }
    const member = await guild.members.fetch(user.id);
    const isSupportStaff = member.roles.cache.some(role => config.supportRoleIds.includes(role.id));
    const isAdmin = member.permissions.has(PermissionsBitField.Flags.Administrator);

    if (!isSupportStaff && !isAdmin) {
        await interaction.reply({ content: "You don't have permission to claim tickets. This action is for support staff or administrators only.", flags: MessageFlags.Ephemeral });
        return;
    }

    try {
console.log(`[claimTicket] State before deferUpdate: ID=${interaction.id}, Replied=${interaction.replied}, Deferred=${interaction.deferred}, Updated=${interaction.isMessageComponent() && interaction.message.flags.has(MessageFlags.Ephemeral) ? 'N/A (ephemeral)' : (interaction.isMessageComponent() ? interaction.message.editedTimestamp : 'N/A')}`);
        await interaction.deferUpdate(); 
        const currentMessages = await channel.messages.fetch({ limit: 10 });
        const botMessages = currentMessages.filter(msg => msg.author.id === client.user.id && msg.embeds.length > 0 && msg.components.length > 0);
        let alreadyClaimed = false;
        let originalTicketMessage;

        for (const msg of botMessages.values()) {
            originalTicketMessage = msg; 
            const row = msg.components[0];
            if (row && row.components.some(c => c.customId && c.customId.startsWith(config.ticketMessages.releaseButtonCustomId))) {
                alreadyClaimed = true;
                break;
            }
             if (row && !row.components.some(c => c.customId && c.customId.startsWith(config.ticketMessages.claimButtonCustomId))) {
                // If claim button is gone, it might be claimed. Check for claim message as a fallback.
                const claimMessageExists = currentMessages.some(m => m.content.includes(config.ticketMessages.ticketClaimedMessage.split(" ")[0])); 
                if (claimMessageExists) {
                    alreadyClaimed = true;
                    break;
                }
            }
        }
        
        if (alreadyClaimed) {
            const existingClaimMessage = currentMessages.find(m => m.embeds[0]?.title === "Ticket Claimed" && m.embeds[0]?.description?.includes(user.tag));
            if (existingClaimMessage) { // Check if the current user is the one who claimed it via embed
                 await interaction.followUp({ content: 'You have already claimed this ticket.', flags: MessageFlags.Ephemeral });
            } else {
                 await interaction.followUp({ content: 'This ticket has already been claimed by another staff member.', flags: MessageFlags.Ephemeral });
            }
            return;
        }
        
        const claimEmbed = buildClaimNotificationEmbed(user, config);
        await channel.send({ embeds: [claimEmbed] });

        if (originalTicketMessage && originalTicketMessage.embeds[0] && originalTicketMessage.components.length > 0) {
            const updatedMainTicketEmbed = buildTicketClaimedEmbed(originalTicketMessage.embeds[0].data, user, config);
            const newButtons = [];
            const originalCloseButtonComponent = originalTicketMessage.components[0].components.find(c => c.customId.startsWith(config.ticketMessages.closeButtonCustomId));
            if (originalCloseButtonComponent) {
                 newButtons.push(ButtonBuilder.from(originalCloseButtonComponent)); 
            }
            const releaseButton = new ButtonBuilder()
                .setCustomId(`${config.ticketMessages.releaseButtonCustomId}_${channel.id}_${user.id}`) 
                .setLabel(config.ticketMessages.releaseButtonLabel)
                .setStyle(ButtonStyle.Secondary)
                .setEmoji('👋');
            newButtons.push(releaseButton);
            const updatedRow = new ActionRowBuilder().addComponents(newButtons);
            await originalTicketMessage.edit({ embeds: [updatedMainTicketEmbed], components: [updatedRow] });
        } else {
            console.warn(`Could not find the original ticket message with embed/buttons in ${channel.name} to update after claim.`);
            await logTicketAction(client, config, `WARNING: Could not find original embed/button message in ${channel.name} to update after claim by ${user.tag}.`, true);
        }
        const oldTopic = channel.topic || `Ticket #${channel.name.split('-').pop()} created by unknown. User's issue: (pending)`;
        const newTopicParts = `Claimed by ${user.tag}. | ${oldTopic}`;
        await updateTicketChannelTopic(channel, newTopicParts, client, config);
        await logTicketAction(client, config, `Ticket ${channel.name} (${channel.id}) claimed by ${user.tag} (${user.id}).`);
    } catch (error) {
        console.error(`Error in claimTicket function for channel ${channel.name}:`, error);
        await logTicketAction(client, config, `ERROR: General error during claimTicket for ${channel.name} by ${user.tag}. Error: ${error.message}`, true);
        // Check if interaction can be replied to or followed up
        if (interaction.replied || interaction.deferred) {
            await interaction.followUp({ content: 'An error occurred while trying to claim this ticket.', flags: MessageFlags.Ephemeral }).catch(console.error);
        } else {
            await interaction.reply({ content: 'An error occurred while trying to claim this ticket.', flags: MessageFlags.Ephemeral }).catch(console.error);
        }
    }
}

export async function releaseTicket(interaction, client, config) {
    const guild = interaction.guild;
    const user = interaction.user; 
    const channel = interaction.channel;
    const parts = interaction.customId.split('_');
    if (parts.length < 3) { // Expects releaseTicket_channelId_claimerId (though claimerId might not be strictly needed for release by any staff)
        console.error(`Invalid customId for release button: ${interaction.customId}`);
        await interaction.reply({ content: 'Error processing this action. Invalid button ID.', flags: MessageFlags.Ephemeral });
        return;
    }
    const ticketChannelId = parts[1];
    // const originalClaimerId = parts[2]; // User who originally claimed

    if (channel.id !== ticketChannelId) {
        await interaction.reply({ content: "This button seems to be for a different ticket channel.", flags: MessageFlags.Ephemeral });
        return;
    }
    const member = await guild.members.fetch(user.id);
    const isSupportStaff = member.roles.cache.some(role => config.supportRoleIds.includes(role.id));
    const isAdmin = member.permissions.has(PermissionsBitField.Flags.Administrator);

    if (!isSupportStaff && !isAdmin) { // Any staff or admin can release
        await interaction.reply({ content: "You don't have permission to release tickets. This action is for support staff or administrators only.", flags: MessageFlags.Ephemeral });
        return;
    }

    try {
        await interaction.deferUpdate();
        const currentMessages = await channel.messages.fetch({ limit: 10 });
        const botMessages = currentMessages.filter(msg => msg.author.id === client.user.id && msg.embeds.length > 0 && msg.components.length > 0);
        let isCurrentlyClaimed = false;
        let originalTicketMessage;
        // let buttonClaimerIdForCheck; 

        for (const msg of botMessages.values()) {
            originalTicketMessage = msg;
            const row = msg.components[0];
            if (row) {
                const releaseButtonComponent = row.components.find(comp => comp.customId && comp.customId.startsWith(config.ticketMessages.releaseButtonCustomId));
                if (releaseButtonComponent) {
                    // buttonClaimerIdForCheck = releaseButtonComponent.customId.split('_')[2];
                    // if (user.id === buttonClaimerIdForCheck || isSupportStaff || isAdmin) { // Check if current user is claimer OR is staff/admin
                    isCurrentlyClaimed = true; 
                    // }
                    break; 
                }
            }
        }

        if (!originalTicketMessage || !isCurrentlyClaimed) {
            await interaction.followUp({ content: 'This ticket does not appear to be claimed, or it cannot be released with this button.', flags: MessageFlags.Ephemeral });
            return;
        }
        
        const releaseEmbed = buildReleaseNotificationEmbed(user, config);
        await channel.send({ embeds: [releaseEmbed] });

        if (originalTicketMessage && originalTicketMessage.embeds[0]) {
            const updatedMainTicketEmbed = buildTicketReleasedEmbed(originalTicketMessage.embeds[0].data, config);
            const newButtons = [];
            const originalCloseButtonComponent = originalTicketMessage.components[0].components.find(c => c.customId.startsWith(config.ticketMessages.closeButtonCustomId));
            
            let originalCreatorIdFromButton = interaction.guild.ownerId; 
            if (originalCloseButtonComponent) {
                newButtons.push(ButtonBuilder.from(originalCloseButtonComponent));
                originalCreatorIdFromButton = originalCloseButtonComponent.customId.split('_')[2]; 
            } else {
                console.error("Could not find original close button data to reconstruct buttons for release.");
                await logTicketAction(client, config, `ERROR: Missing original close button data in ${channel.name} during release by ${user.tag}.`, true);
            }
            
            const claimButton = new ButtonBuilder()
                .setCustomId(`${config.ticketMessages.claimButtonCustomId}_${channel.id}_${originalCreatorIdFromButton}`) 
                .setLabel(config.ticketMessages.claimButtonLabel)
                .setStyle(ButtonStyle.Primary)
                .setEmoji('🙋');
            newButtons.push(claimButton);

            const updatedRow = new ActionRowBuilder().addComponents(newButtons);
            await originalTicketMessage.edit({ embeds: [updatedMainTicketEmbed], components: [updatedRow] });
        }
        
        if (channel.topic && channel.topic.includes("Claimed by ")) { 
            const topicParts = channel.topic.split("|");
            const originalTopic = topicParts.length > 1 ? topicParts.slice(1).join("|").trim() : `Ticket #${channel.name.split('-').pop()} - Opened by...`; 
            await updateTicketChannelTopic(channel, originalTopic, client, config);
        }

        await logTicketAction(client, config, `Ticket ${channel.name} (${channel.id}) released by ${user.tag} (${user.id}).`);

    } catch (error) {
        console.error(`Error in releaseTicket function for channel ${channel.name}:`, error);
        await logTicketAction(client, config, `ERROR: General error during releaseTicket for ${channel.name} by ${user.tag}. Error: ${error.message}`, true);
        if (interaction.replied || interaction.deferred) {
            await interaction.followUp({ content: 'An error occurred while trying to release this ticket.', flags: MessageFlags.Ephemeral }).catch(console.error);
        } else {
            await interaction.reply({ content: 'An error occurred while trying to release this ticket.', flags: MessageFlags.Ephemeral }).catch(console.error);
        }
    }
}