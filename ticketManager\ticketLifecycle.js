// ticketManager/ticketLifecycle.js
import { ChannelType, PermissionsBitField, ButtonBuilder, ButtonStyle, ActionRowBuilder, MessageFlags } from 'discord.js';
import { getNextTicketNumber, formatTicketNumber } from '../utils.js'; // Adjusted path
import { findOrCreateTicketCategory, createTicketChannel, deleteTicketChannel, sanitizeUsernameForChannel } from './ticketChannels.js'; // Added sanitizeUsernameForChannel
import { logTicketAction } from './ticketLogging.js';
import { buildInitialTicketEmbed, buildCloseConfirmationEmbed, buildTicketClosedEmbed } from './ticketEmbeds.js';
import { generateTranscriptFile, archiveTranscript, deleteTranscriptFile } from './transcriptService.js';
// fs is used by deleteTranscriptFile, which is now part of transcriptService.js

export async function createTicket(interaction, client, config, ticketSubject, ticketReason, ticketOrderId, ticketType) {
    const guild = interaction.guild;
    const user = interaction.user;

    try {
        if (interaction.isModalSubmit() && !interaction.deferred && !interaction.replied) {
console.log(`[ModalSubmit] State before deferReply: ID=${interaction.id}, Replied=${interaction.replied}, Deferred=${interaction.deferred}`);
            await interaction.deferReply({ flags: MessageFlags.Ephemeral });
        // The actual ticket creation logic will now proceed.
        }

        const ticketCategory = await findOrCreateTicketCategory(guild, client, config);
        if (!ticketCategory) {
            await interaction.editReply({ content: 'Could not find or create the ticket category. Please check my permissions.', flags: MessageFlags.Ephemeral });
            return; 
        }

        const sanitizedUsername = sanitizeUsernameForChannel(user.username); // This needs to be imported or defined if not already
        let channelPrefix = "ticket"; // Default prefix
        if (ticketType === 'order_issues') {
            channelPrefix = "order-issue";
        } else if (ticketType === 'request_payout') {
            channelPrefix = "payout-request";
        }
        const proposedChannelName = `${channelPrefix}-${sanitizedUsername}`;

        if (ticketCategory) {
            const existingChannel = ticketCategory.children.cache.find(
                ch => ch.name === proposedChannelName && ch.type === ChannelType.GuildText
            );
            if (existingChannel) {
                await interaction.editReply({ content: `You already have an open ticket: ${existingChannel}`, flags: MessageFlags.Ephemeral });
                return;
            }
        }

        const ticketNumber = await getNextTicketNumber(guild.id, config.ticketNumberPersistenceFile);
        const formattedTicketNumber = formatTicketNumber(ticketNumber);
        const channelName = proposedChannelName;

        const ticketChannel = await createTicketChannel(guild, user, client, config, ticketCategory, channelName, formattedTicketNumber, ticketSubject, ticketOrderId);

        if (!ticketChannel) {
            await interaction.editReply({ content: 'Could not create your ticket channel. Please check my permissions.', flags: MessageFlags.Ephemeral });
            return;
        }

        // Update the user that the ticket channel is created and provide a link
        if (interaction.deferred || interaction.replied) { // Check if we can edit the reply
            await interaction.editReply({ content: `Your ticket <#${ticketChannel.id}> has been created! Click the link to jump to it.`, flags: MessageFlags.Ephemeral });
        } else {
            // This case should ideally not be hit if deferReply was successful
            await interaction.reply({ content: `Your ticket <#${ticketChannel.id}> has been created! Click the link to jump to it.`, flags: MessageFlags.Ephemeral });
        }

        const supportRoleMentions = config.supportRoleIds.map(id => `<@&${id}>`).join(' ') || "";
        const professionalTicketEmbed = buildInitialTicketEmbed(user, config, formattedTicketNumber, ticketSubject, ticketReason, ticketOrderId, ticketType);
        // const adminRoleId = "599098034831228959"; // Removed hardcoded admin role
        // const adminRoleMention = `<@&${adminRoleId}>`; // Removed hardcoded admin role mention
        const pingContent = supportRoleMentions ? `${user.toString()} ${supportRoleMentions}` : `${user.toString()}`; // User mention + support roles, or just user if no support roles

        const closeButton = new ButtonBuilder()
            .setCustomId(`${config.ticketMessages.closeButtonCustomId}_${ticketChannel.id}_${user.id}`)
            .setLabel(config.ticketMessages.closeButtonLabel)
            .setStyle(ButtonStyle.Danger)
            .setEmoji('🔒');

        const claimButton = new ButtonBuilder() // This button might belong more with ticketActions if it's purely an action
            .setCustomId(`${config.ticketMessages.claimButtonCustomId}_${ticketChannel.id}_${user.id}`)
            .setLabel(config.ticketMessages.claimButtonLabel)
            .setStyle(ButtonStyle.Primary)
            .setEmoji('🙋');

        const ticketActionRow = new ActionRowBuilder().addComponents(closeButton, claimButton);

        try {
            await ticketChannel.send({
                content: pingContent, 
                embeds: [professionalTicketEmbed],
                components: [ticketActionRow],
            });
        } catch (error) {
            console.error(`Error sending initial message to ${ticketChannel.name}:`, error);
            await logTicketAction(client, config, `ERROR: Failed to send initial message to ${ticketChannel.name}. Error: ${error.message}`, true);
        }

        await logTicketAction(client, config, `Ticket #${formattedTicketNumber} (${ticketChannel.id}) created by ${user.tag} (${user.id}). Type: ${ticketType}. Subject: "${ticketSubject}". Order ID: ${ticketOrderId}`);

    } catch (error) {
        console.error('General error in createTicket function:', error);
        if (interaction.replied || interaction.deferred) {
             await interaction.followUp({ content: 'An unexpected error occurred while creating your ticket.', flags: MessageFlags.Ephemeral }).catch(console.error);
        } else {
            await interaction.reply({ content: 'An unexpected error occurred while creating your ticket.', flags: MessageFlags.Ephemeral }).catch(console.error);
        }
        await logTicketAction(client, config, `ERROR: General error in createTicket for ${user.tag}. Error: ${error.message}`, true);
    }
}

export async function handleCloseTicketButton(interaction, client, config) {
    const guild = interaction.guild;
    const user = interaction.user;
    const channel = interaction.channel;
    const parts = interaction.customId.split('_');
    if (parts.length < 3) {
        console.error(`Invalid customId for close button: ${interaction.customId}`);
        await interaction.reply({ content: 'Error processing this action. Invalid button ID.', flags: MessageFlags.Ephemeral });
        return;
    }
    const ticketChannelId = parts[1];
    const ticketCreatorId = parts[2];
    const member = await guild.members.fetch(user.id);
    const isSupportStaff = member.roles.cache.some(role => config.supportRoleIds.includes(role.id));
    const isTicketCreator = user.id === ticketCreatorId;
    const isAdmin = member.permissions.has(PermissionsBitField.Flags.Administrator);

    if (!isAdmin) {
        await interaction.reply({ content: "You don't have permission to initiate closing this ticket.", flags: MessageFlags.Ephemeral });
        return;
    }
    if (channel.id !== ticketChannelId) {
        await interaction.reply({ content: "This button seems to be for a different ticket channel.", flags: MessageFlags.Ephemeral });
        return;
    }

    const confirmEmbed = buildCloseConfirmationEmbed(user, config);
    const confirmCloseButton = new ButtonBuilder()
        .setCustomId(`confirmClose_${channel.id}_${user.id}_${ticketCreatorId}`)
        .setLabel('Yes, close it')
        .setStyle(ButtonStyle.Danger);
    const cancelCloseButton = new ButtonBuilder()
        .setCustomId(`cancelClose_${channel.id}`)
        .setLabel('Cancel')
        .setStyle(ButtonStyle.Secondary);
    const confirmationRow = new ActionRowBuilder().addComponents(confirmCloseButton, cancelCloseButton);
    try {
        await interaction.reply({ embeds: [confirmEmbed], components: [confirmationRow], flags: MessageFlags.Ephemeral });
    } catch (error) {
        console.error(`Error sending close confirmation for ticket ${channel.id}:`, error);
        await logTicketAction(client, config, `ERROR: Failed to send close confirmation for ticket ${channel.name} by ${user.tag}. Error: ${error.message}`, true);
    }
}

export async function processTicketClosure(interaction, client, config) { // Renamed from closeTicket
    const guild = interaction.guild;
    const user = interaction.user; 
    const channel = interaction.channel; 
    const parts = interaction.customId.split('_');
    if (parts.length < 4) { 
        console.error(`Invalid customId for confirm close button: ${interaction.customId}`);
        await interaction.update({ content: 'Error processing this action. Invalid button ID.', components: []});
        return;
    }
    const targetChannelId = parts[1];

    if (channel.id !== targetChannelId) {
        await interaction.update({ content: "This confirmation is for a different ticket channel.", components: []});
        return;
    }

    try {
        if (!interaction.deferred && !interaction.replied) {
            await interaction.deferUpdate();
        }
        
        const messages = await channel.messages.fetch({ limit: 20 }); 
        const botTicketMessage = messages.find(m => m.author.id === client.user.id && m.embeds.length > 0 && m.embeds[0].title && m.embeds[0].title.includes("Ticket #"));
        
        if (botTicketMessage && botTicketMessage.embeds[0]) {
            const updatedEmbed = buildTicketClosedEmbed(botTicketMessage.embeds[0].data, user, config);
            await botTicketMessage.edit({ embeds: [updatedEmbed], components: [] });
        }

        if(interaction.isButton() && (interaction.replied || interaction.deferred)) {
             await interaction.followUp({ content: 'Ticket is being closed...', flags: MessageFlags.Ephemeral });
        } else if (!interaction.replied && !interaction.deferred) {
             await interaction.reply({ content: 'Ticket is being closed...', flags: MessageFlags.Ephemeral });
        }

        let transcriptAttachment;
        if (config.transcripts.enabled) {
            try {
                transcriptAttachment = await generateTranscriptFile(channel, client, config);
                if (transcriptAttachment) {
                    await archiveTranscript(transcriptAttachment, channel, user, client, config);
                }
            } catch (transcriptError) {
                console.error(`Error during transcript generation or archiving for ${channel.name}:`, transcriptError);
                await channel.send({ content: `Could not process the transcript for this ticket. An error occurred.`}).catch(console.error);
            }
        }

        const closerMention = `<@${user.id}>`;
        const logMsg = (config.ticketMessages.ticketClosedMessage || 'Ticket closed by {closerMention}.')
            .replace('{closerMention}', closerMention);
        await channel.send(logMsg); 
        await logTicketAction(client, config, `Ticket ${channel.name} (${channel.id}) closed by ${user.tag} (${user.id}).`);

        await deleteTicketChannel(channel, user, `Ticket closed by ${user.tag}`, client, config);

        if (transcriptAttachment && transcriptAttachment.attachment) {
            await deleteTranscriptFile(transcriptAttachment.attachment, client, config);
        }

    } catch (error) {
        console.error(`Error in processTicketClosure function for channel ${channel.name}:`, error);
        await logTicketAction(client, config, `ERROR: General error during processTicketClosure for ${channel.name} by ${user.tag}. Error: ${error.message}`, true);
        try {
            if (interaction.channel && !interaction.channel.deleted && (interaction.replied || interaction.deferred)) { 
                 await interaction.followUp({ content: 'An error occurred while trying to close this ticket. Please contact an administrator.', flags: MessageFlags.Ephemeral}).catch(console.error);
            } else if (interaction.channel && !interaction.channel.deleted && !interaction.replied && !interaction.deferred){
                 await interaction.reply({ content: 'An error occurred while trying to close this ticket. Please contact an administrator.', flags: MessageFlags.Ephemeral}).catch(console.error);
            }
        } catch (e) { /* channel might not be available or interaction already handled */ }
    }
}

// Need to import sanitizeUsernameForChannel if it's used directly here, or ensure it's only used within ticketChannels.js
// It seems sanitizeUsernameForChannel is used in createTicket, so it should be imported in ticketChannels.js and used there.
// Correcting the createTicket function to use sanitizeUsernameForChannel from ticketChannels.js
// This was a note for myself, the import is already in ticketChannels.js