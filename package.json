{"name": "discord-ticket-bot", "version": "1.0.0", "description": "A Discord bot for managing support tickets.", "main": "index.js", "type": "module", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "deploy": "node deploy-commands.js"}, "keywords": ["discord", "bot", "ticket", "support"], "author": "", "license": "ISC", "dependencies": {"discord.js": "^14.15.3", "dotenv": "^16.4.5"}, "devDependencies": {"nodemon": "^3.1.4"}}