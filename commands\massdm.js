import { <PERSON><PERSON><PERSON><PERSON>mand<PERSON><PERSON>er, MessageFlags, PermissionsBitField, EmbedBuilder } from 'discord.js';
// Config will be passed via execute function, so no direct loadConfig here needed unless for a default.

export default {
    data: new SlashCommandBuilder()
        .setName('massdm')
        .setDescription('Sends a direct message to all members of a specified role.')
        .addRoleOption(option =>
            option.setName('role')
                .setDescription('The role to send a message to')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('message')
                .setDescription('The message to send')
                .setRequired(true))
        .setDefaultMemberPermissions(0) // Initially no default permissions, handled by role check
        .setDMPermission(false),

    async execute(interaction, client, config) {
        const targetRole = interaction.options.getRole('role');
        const messageContent = interaction.options.getString('message');

        // Permission Check: User must be a server admin OR have at least one of the supportRoleIds
        const isAdmin = interaction.member.permissions.has(PermissionsBitField.Flags.Administrator);
        const memberHasSupportRole = config.supportRoleIds && config.supportRoleIds.length > 0 && interaction.member.roles.cache.some(role => config.supportRoleIds.includes(role.id));

        if (!isAdmin && !memberHasSupportRole) {
            await interaction.reply({ content: 'You do not have permission to use this command. You need to be an Administrator or have a designated support role.', flags: [MessageFlags.Ephemeral] });
            return;
        }

        if (!targetRole) { // Should be caught by setRequired, but good practice
            await interaction.reply({ content: 'You must specify a role.', flags: [MessageFlags.Ephemeral] });
            return;
        }


        if (!messageContent || messageContent.trim() === '') {
            await interaction.reply({ content: 'Message content cannot be empty.', flags: [MessageFlags.Ephemeral] });
            return;
        }

        try {
            // Defer the reply as sending DMs can take time
            await interaction.deferReply({ flags: [MessageFlags.Ephemeral] });

            // Fetch members with the role, as role.members might not be complete
            await interaction.guild.members.fetch(); // Ensure member cache is up-to-date
            const membersWithRole = interaction.guild.members.cache.filter(member => member.roles.cache.has(targetRole.id) && !member.user.bot);


            if (membersWithRole.size === 0) {
                await interaction.editReply({ content: `No non-bot members found in the role ${targetRole.name}.` });
                return;
            }

            const embed = new EmbedBuilder()
                .setColor(config.embedColors?.default || '#ffa300')
                .setDescription(messageContent)
                .setImage('https://i.imgur.com/gyKQO9G.png')
                .setFooter({ text: 'BuyBoosting.com' });

            let successCount = 0;
            let failCount = 0;

            const sendPromises = membersWithRole.map(member =>
                member.send({ embeds: [embed] })
                    .then(() => { successCount++; })
                    .catch(error => {
                        console.error(`Failed to send DM to ${member.user.tag} (ID: ${member.id}):`, error.message);
                        failCount++;
                    })
            );

            await Promise.allSettled(sendPromises);

            await interaction.editReply({
                content: `Message sending complete for role ${targetRole.name}.\nSuccessfully sent to: ${successCount} members.\nFailed to send to: ${failCount} members.`,
            });

        } catch (error) {
            console.error('Error executing massdm command:', error);
            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ content: 'There was an error while executing this command.', flags: [MessageFlags.Ephemeral] });
            } else {
                await interaction.reply({ content: 'There was an error while executing this command!', flags: [MessageFlags.Ephemeral] });
            }
        }
    }
};