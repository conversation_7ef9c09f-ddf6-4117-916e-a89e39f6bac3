// commands/remove.js
import { Slash<PERSON><PERSON>mandBuilder, PermissionsBitField, ChannelType, EmbedBuilder, MessageFlags } from 'discord.js';
import { logTicketAction } from '../ticketManager/index.js'; // Explicitly point to the barrel file

export const data = new SlashCommandBuilder()
    .setName('remove')
    .setDescription('Removes a user from the current ticket channel.')
    .addUserOption(option =>
        option.setName('user')
            .setDescription('The user to remove from this ticket channel.')
            .setRequired(true))
    .setDefaultMemberPermissions(PermissionsBitField.Flags.Administrator)
    .setDMPermission(false);

export async function execute(interaction, client, config) {
        const targetUser = interaction.options.getUser('user');
        const channel = interaction.channel;
        const member = interaction.member; // Member who executed the command

        if (!member.permissions.has(PermissionsBitField.Flags.Administrator)) {
            return interaction.reply({ content: 'You do not have permission to use this command.', flags: MessageFlags.Ephemeral });
        }

        const ticketCategory = interaction.guild.channels.cache.find(
            c => c.name === config.ticketCategoryName && c.type === ChannelType.GuildCategory
        );

        const validPrefixes = ['ticket-', 'order-issue-', 'payout-request-'];
        const hasValidPrefix = validPrefixes.some(prefix => channel.name.startsWith(prefix));

        if (!hasValidPrefix || (ticketCategory && channel.parentId !== ticketCategory.id)) {
            return interaction.reply({ content: 'This command can only be used in a ticket channel.', flags: MessageFlags.Ephemeral });
        }

        if (!targetUser) {
            return interaction.reply({ content: 'Could not find the specified user.', flags: MessageFlags.Ephemeral });
        }

        // Prevent removing the bot itself or the ticket creator (unless you want to allow this)
        // For now, let's assume the ticket creator should not be removed this way.
        // The original ticket creator's permissions are usually tied to the ticket's existence.
        // If the channel topic stores creator ID:
        // const topicCreatorId = channel.topic.match(/(\d{17,19})/)?.[0];
        // if (targetUser.id === topicCreatorId) {
        //    return interaction.reply({ content: 'You cannot remove the original ticket creator using this command. Close the ticket instead.', ephemeral: true });
        // }
        if (targetUser.id === client.user.id) {
            return interaction.reply({ content: 'You cannot remove the bot itself from the ticket.', flags: MessageFlags.Ephemeral });
        }


        try {
            const currentPermissions = channel.permissionOverwrites.cache.get(targetUser.id);
            if (!currentPermissions) {
                return interaction.reply({ content: `${targetUser.tag} does not have explicit permissions in this ticket to remove. They might have access via a role.`, flags: MessageFlags.Ephemeral });
            }

            await channel.permissionOverwrites.delete(targetUser.id, `Removed by ${member.user.tag}`);

            const removeEmbed = new EmbedBuilder()
                .setColor(config.ticketPanel.embedColor || '#FF0000') // Red for removal, or use panel color
                .setTitle('User Removed from Ticket')
                .setDescription(`${targetUser.toString()} has been removed from this ticket.`)
                .addFields({ name: 'Removed by', value: member.toString(), inline: true })
                .setTimestamp();

            await channel.send({ embeds: [removeEmbed] });
            // The interaction reply should be ephemeral if the main confirmation is in the channel
            await interaction.reply({ content: `${targetUser.tag} has been successfully removed from this ticket. A notification has been posted in the channel.`, flags: MessageFlags.Ephemeral });
            await logTicketAction(client, config, `${member.user.tag} removed ${targetUser.tag} from ticket ${channel.name} (${channel.id}).`);

        } catch (error) {
            console.error(`Error removing user ${targetUser.tag} from ticket ${channel.name}:`, error);
            await logTicketAction(client, config, `ERROR: Failed to remove ${targetUser.tag} from ticket ${channel.name} by ${member.user.tag}. Error: ${error.message}`, true);
            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ content: 'There was an error trying to remove the user from this ticket.', flags: MessageFlags.Ephemeral });
            } else {
                await interaction.reply({ content: 'There was an error trying to remove the user from this ticket.', flags: MessageFlags.Ephemeral });
            }
        }
    }
// Removed module.exports wrapper