import { SlashCommandBuilder, EmbedBuilder } from 'discord.js';

export default {
    data: new SlashCommandBuilder()
        .setName('tiktok')
        .setDescription('Sends a link to the TikTok account.'),
    async execute(interaction) {
        const tiktokEmbed = new EmbedBuilder()
            .setColor('#ffa300') // User specified color
            .setTitle('Our TikTok Channel!')
            .setURL('https://www.tiktok.com/@buyboosting')
            .setDescription('Check out our latest videos on TikTok and follow us!\nClick the title to go directly to the profile.')
            .setThumbnail('https://i.imgur.com/JpbgHKG.png') // User specified logo
            .addFields({ name: 'TikTok Profile', value: '[@buyboosting](https://www.tiktok.com/@buyboosting)' })
            .setTimestamp()
            .setFooter({ text: 'BuyBoosting TikTok', iconURL: 'https://i.imgur.com/JpbgHKG.png' }); // User specified logo

        await interaction.reply({ embeds: [tiktokEmbed] });
    },
};