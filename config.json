{"botTokenEnv": "DISCORD_TOKEN", "clientIdEnv": "CLIENT_ID", "guildIdEnv": "GUILD_ID", "ticketCategoryName": "Support Tickets", "supportRoleIdsEnv": "1375556935520223355", "logChannelIdEnv": "LOG_CHANNEL_ID", "specialOffersRoleId": "1375942112377311312", "autoAssignRoleIdOnJoin": "1375559566389153852", "sendWelcomeDmOnJoin": true, "welcomeDmSettings": {"buttonLabel": "Visit BuyBoosting.com", "buttonUrl": "https://buyboosting.com", "imageUrl": "https://i.imgur.com/bUsQB4i.gif", "footerText": "Welcome to the BuyBoosting Community!"}, "ticketPanel": {"embedTitle": "{panelE<PERSON>ji} Create a New Support Ticket", "defaultPanelEmoji": "📨", "embedDescription": "Need assistance? Click the button below to open a new support ticket. Our team will get back to you as soon as possible.\n\nPlease be ready to provide a clear subject and a detailed description of your issue in the form that follows.", "embedColor": "#ffa300", "buttonLabel": "Open Support Ticket", "footerText": "BuyBoosting.com | Support system", "buttonCustomId": "createTicket", "buttonEmojiId": "1043394152018223174"}, "specialOffersPanel": {"title": "Notification Role Management", "thumbnailUrl": "", "description": "Please use the buttons below to subscribe to or unsubscribe from Special Offer notifications.", "fieldName": "Role Benefits", "fieldValue": "Subscribers receive notifications about exclusive discounts, promotions, and early access to new services.", "footerText": "BuyBoosting.com | Role Management", "getRoleButtonLabel": "Subscribe to Special Offers", "getRoleButtonEmoji": {"id": "779046189956857867", "animated": true}, "removeRoleButtonLabel": "Unsubscribe from Special Offers", "removeRoleButtonEmoji": {"id": "779046189744128050", "animated": true}}, "ticketMessages": {"initialGreeting": "Hello {userMention}! Welcome to your support ticket.\nPlease describe your issue in as much detail as possible.", "closeButtonLabel": "Close Ticket", "closeButtonCustomId": "closeTicket", "claimButtonLabel": "<PERSON><PERSON><PERSON>", "claimButtonCustomId": "claimTicket", "releaseButtonLabel": "Release Ticket", "releaseButtonCustomId": "releaseTicket", "confirmCloseMessage": "Are you sure you want to close this ticket?", "ticketClosingSoonMessage": "This ticket will be closed in {seconds} seconds.", "ticketClosedMessage": "Ticket closed by {closerMention}.", "ticketClaimedMessage": "This ticket is now being handled by {claimerMention}.", "ticketReleasedMessage": "This ticket has been released by {releaserMention} and is now available for all support staff.", "defaultSubject": "No Subject Provided", "unassignedUser": "Unassigned"}, "ticketStatus": {"new": {"text": "New", "icon": "🆕", "color": "#5865F2"}, "open": {"text": "Open", "icon": "🔵", "color": "#72B5F2"}, "claimed": {"text": "Claimed", "icon": "🙋", "color": "#FAA61A"}, "inProgress": {"text": "In Progress", "icon": "🟠", "color": "#FAA61A"}, "pending": {"text": "Pending Input", "icon": "🟡", "color": "#FEE75C"}, "resolved": {"text": "Resolved", "icon": "✅", "color": "#57F287"}, "closed": {"text": "Closed", "icon": "🔒", "color": "#99AAB5"}}, "embedColors": {"default": "#ffa300"}, "transcripts": {"enabled": true, "logChannelIdEnv": "TRANSCRIPT_LOG_CHANNEL_ID", "format": "html"}, "ticketCloseDelaySeconds": 15, "ticketNumberPersistenceFile": "ticketCount.json"}