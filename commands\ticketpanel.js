// commands/ticketpanel.js
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er, EmbedBuilder, ButtonBuilder, ButtonStyle, ActionRowBuilder, PermissionsBitField, MessageFlags } from 'discord.js';

export const data = new SlashCommandBuilder()
    .setName('ticketpanel')
    .setDescription('Posts the ticket creation panel in the current channel.')
    .setDefaultMemberPermissions(PermissionsBitField.Flags.Administrator) // Only admins can use this
    .setDMPermission(false); // Disable in DMs

export async function execute(interaction, client, config) {
        try {
            await interaction.deferReply({ ephemeral: true }); // Defer the reply

            const { embedTitle, embedDescription, embedColor, buttonLabel, buttonCustomId, footerText, thumbnailUrl, buttonEmojiId } = config.ticketPanel;
            // const guildName = interaction.guild.name; // No longer needed for footer

            const panelEmbed = new EmbedBuilder()
                .setTitle(embedTitle || '📨 Create a New Support Ticket')
                .setDescription(embedDescription || 'Click the button below to open a new support ticket.')
                .setColor(config.embedColors?.default || embedColor || '#ffa300') // Use global default
                .setImage('https://i.imgur.com/HnBbFeZ.gif');

            if (footerText) {
                panelEmbed.setFooter({ text: footerText }); // Use footerText directly
            } else {
                // Fallback if footerText is somehow empty, though config.json has a default
                panelEmbed.setFooter({ text: interaction.guild.name });
            }

            if (thumbnailUrl && thumbnailUrl.startsWith('http')) {
                panelEmbed.setThumbnail(thumbnailUrl);
            }


            const createTicketButton = new ButtonBuilder()
                .setCustomId(buttonCustomId)
                .setLabel(buttonLabel)
                .setStyle(ButtonStyle.Primary); // Changed to Primary for blue
            
            if (buttonEmojiId) {
                createTicketButton.setEmoji(buttonEmojiId);
            } else {
                createTicketButton.setEmoji('🎟️'); // Fallback emoji
            }


            const supportLinkButton = new ButtonBuilder()
                .setLabel('Support Website')
                .setURL('https://buyboosting.com/support') // Changed back to Link button
                .setStyle(ButtonStyle.Link)
                .setEmoji('🌐');

            const row = new ActionRowBuilder().addComponents(createTicketButton); // Removed supportLinkButton

            const additionalInfoEmbed = new EmbedBuilder()
                .setColor(config.embedColors?.default || '#ffa300') // Use global default color
                .setDescription("For other support options, you can visit our **[Support Website](https://buyboosting.com/support)** (response times may be delayed compared to opening a ticket here). You can also reach us via Live Chat: just visit https://buyboosting.com and check the bottom right corner (Desktop users).");

            await interaction.channel.send({ embeds: [panelEmbed, additionalInfoEmbed], components: [row] });
            await interaction.editReply({ content: 'Ticket panel successfully posted with rephrased additional info!'}); // Use editReply

        } catch (error) {
            console.error('Error executing ticketpanel command:', error);
            // Since we deferred, we should use editReply or followUp
            if (interaction.deferred || interaction.replied) { // Check if deferred or already replied (e.g. by deferReply)
                await interaction.editReply({ content: 'There was an error posting the ticket panel. Please check my permissions.' });
            } else {
                // This case should ideally not be reached if deferReply is used correctly
                await interaction.reply({ content: 'There was an error posting the ticket panel. Please check my permissions.', flags: MessageFlags.Ephemeral });
            }
        }
    }
// Removed module.exports wrapper