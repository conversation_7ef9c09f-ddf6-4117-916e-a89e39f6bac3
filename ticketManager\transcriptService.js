// ticketManager/transcriptService.js
import fs from 'node:fs';
import path from 'node:path';
import { fileURLToPath } from 'node:url';
import { AttachmentBuilder } from 'discord.js';
import { logTicketAction } from './ticketLogging.js';

// Recreate __dirname for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Generates an HTML transcript for a given channel.
 * @param {import('discord.js').TextChannel} channel The channel to transcribe.
 * @param {import('discord.js').Client} client The Discord client.
 * @param {object} config The bot's configuration object.
 * @returns {Promise<AttachmentBuilder|null>} An AttachmentBuilder for the transcript file, or null if failed.
 */
export async function generateTranscriptFile(channel, client, config) {
    if (!channel || !channel.isTextBased()) {
        console.error('Invalid channel provided for transcript generation.');
        await logTicketAction(client, config, `ERROR: Invalid channel provided for transcript generation (ID: ${channel?.id})`, true);
        return null;
    }

    const guildName = channel.guild.name;
    const channelName = channel.name;
    // Attempt to extract ticket number more robustly if present in channel name or topic
    let ticketNumberDisplay = channelName; 
    const nameMatch = channelName.match(/ticket-(\w+)-(\d+)/) || channelName.match(/ticket-(\d+)/);
    if (nameMatch && nameMatch.length > 1) {
        ticketNumberDisplay = nameMatch[nameMatch.length -1];
    } else if (channel.topic && channel.topic.includes("Ticket #")) {
        const topicMatch = channel.topic.match(/Ticket #(\d+)/);
        if (topicMatch && topicMatch[1]) {
            ticketNumberDisplay = topicMatch[1];
        }
    }


    let htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ticket Transcript - ${channelName}</title>
    <style>
        body { font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; margin: 0; padding: 0; background-color: #f4f4f4; color: #333; }
        .container { max-width: 800px; margin: 20px auto; background-color: #fff; border-radius: 8px; box-shadow: 0 0 10px rgba(0,0,0,0.1); padding: 20px; }
        .header { border-bottom: 1px solid #eee; padding-bottom: 10px; margin-bottom: 20px; }
        .header h1 { margin: 0; font-size: 24px; color: #5865F2; } /* Discord blue-ish */
        .header p { margin: 5px 0 0; color: #777; font-size: 14px; }
        .message-group { margin-bottom: 20px; display: flex; }
        .avatar { width: 40px; height: 40px; border-radius: 50%; margin-right: 15px; }
        .message-content { flex-grow: 1; }
        .message-header { display: flex; align-items: center; margin-bottom: 5px; }
        .username { font-weight: bold; color: #2c2f33; margin-right: 8px; }
        .timestamp { font-size: 12px; color: #72767d; }
        .message-text { line-height: 1.6; white-space: pre-wrap; word-wrap: break-word; }
        .message-text p { margin: 0 0 5px 0; } /* Ensure paragraphs within message text have some spacing */
        .attachment a { color: #0068E0; text-decoration: none; }
        .attachment a:hover { text-decoration: underline; }
        .embed { border-left: 4px solid #ccc; padding: 10px; margin-top: 5px; background-color: #f9f9f9; border-radius: 4px; }
        .embed-title { font-weight: bold; margin-bottom: 5px; }
        .embed-description { font-size: 0.9em; }
        .embed-field { margin-bottom: 3px; }
        .embed-field-name { font-weight: bold; }
        .footer { text-align: center; margin-top: 30px; padding-top: 15px; border-top: 1px solid #eee; font-size: 12px; color: #777; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Ticket Transcript</h1>
            <p><strong>Server:</strong> ${guildName}</p>
            <p><strong>Ticket:</strong> ${channelName} (ID: ${ticketNumberDisplay})</p>
            <p><strong>Generated on:</strong> ${new Date().toUTCString()}</p>
        </div>
`;

    let messages = [];
    let lastId;

    while (true) {
        const fetchedMessages = await channel.messages.fetch({ limit: 100, before: lastId });
        if (fetchedMessages.size === 0) {
            break;
        }
        messages = messages.concat(Array.from(fetchedMessages.values()));
        lastId = fetchedMessages.lastKey();
    }
    messages.reverse(); // To display in chronological order

    for (const msg of messages) {
        const timestamp = new Date(msg.createdTimestamp).toLocaleString('en-US', { 
            year: 'numeric', month: '2-digit', day: '2-digit',
            hour: '2-digit', minute: '2-digit', second: '2-digit',
            timeZone: 'UTC' // Standardize to UTC
        });
        const avatarURL = msg.author.displayAvatarURL({ dynamic: true, size: 64 });

        htmlContent += `
        <div class="message-group">
            <img src="${avatarURL}" alt="${msg.author.tag}'s avatar" class="avatar">
            <div class="message-content">
                <div class="message-header">
                    <span class="username">${msg.author.tag}</span>
                    <span class="timestamp">${timestamp} UTC</span>
                </div>
                <div class="message-text"><p>${msg.content.replace(/\n/g, '<br>') || '&nbsp;'}</p></div>`; // Handle empty messages
        
        if (msg.attachments.size > 0) {
            msg.attachments.forEach(att => {
                htmlContent += `<div class="attachment"><a href="${att.url}" target="_blank">[Attachment: ${att.name}]</a></div>`;
            });
        }
        if (msg.embeds.length > 0) {
            msg.embeds.forEach((embed) => { // Removed index as it's not used
                htmlContent += `<div class="embed" style="border-left-color: #${embed.hexColor || 'ccc'};">`;
                if (embed.title) htmlContent += `<div class="embed-title">${embed.title}</div>`;
                if (embed.description) htmlContent += `<div class="embed-description">${embed.description.replace(/\n/g, '<br>')}</div>`;
                if (embed.fields && embed.fields.length > 0) {
                    htmlContent += '<div>';
                    embed.fields.forEach(field => {
                        htmlContent += `<div class="embed-field"><strong>${field.name}:</strong><br>${field.value.replace(/\n/g, '<br>')}</div>`;
                    });
                    htmlContent += '</div>';
                }
                if (embed.footer && embed.footer.text) htmlContent += `<div class="embed-footer" style="font-size:0.8em; color:#777; margin-top:5px;">${embed.footer.text}</div>`;
                htmlContent += `</div>`;
            });
        }
        htmlContent += `</div></div>`;
    }

    htmlContent += `
    <div class="footer">
        End of Transcript
    </div>
</div>
</body>
</html>`;

    const transcriptDir = path.join(__dirname, '..', 'transcripts'); // Go up one level from ticketManager
    if (!fs.existsSync(transcriptDir)) {
        try {
            fs.mkdirSync(transcriptDir, { recursive: true });
        } catch (mkdirError) {
            console.error(`Error creating transcript directory ${transcriptDir}:`, mkdirError);
            await logTicketAction(client, config, `ERROR: Failed to create transcript directory ${transcriptDir}. Error: ${mkdirError.message}`, true);
            return null;
        }
    }

    const fileName = `transcript-${channelName.replace(/[^a-z0-9-_]/gi, '_')}-${Date.now()}.html`;
    const filePath = path.join(transcriptDir, fileName);

    try {
        fs.writeFileSync(filePath, htmlContent);
        console.log(`Transcript saved: ${filePath}`);
        return new AttachmentBuilder(filePath, { name: fileName });
    } catch (error) {
        console.error('Error writing transcript file:', error);
        await logTicketAction(client, config, `ERROR: Failed to write transcript file for ${channelName}. Error: ${error.message}`, true);
        return null;
    }
}

/**
 * Archives the transcript by sending it to the configured log channel.
 * @param {AttachmentBuilder} transcriptAttachment The transcript attachment.
 * @param {import('discord.js').TextChannel} ticketChannel The channel the transcript is for.
 * @param {import('discord.js').User} closerUser The user who closed the ticket.
 * @param {import('discord.js').Client} client The Discord client.
 * @param {object} config The bot's configuration object.
 */
export async function archiveTranscript(transcriptAttachment, ticketChannel, closerUser, client, config) {
    if (!transcriptAttachment || !config.transcripts.logChannelId) {
        if (transcriptAttachment) { // Log channel not configured, send to ticket channel if possible
             try {
                await ticketChannel.send({ content: `Transcript generated. (Log channel not configured)`, files: [transcriptAttachment] });
             } catch (e) { console.error(`Error sending transcript to ticket channel ${ticketChannel.name} as fallback:`, e); }
        }
        if (!config.transcripts.logChannelId) {
            console.warn(`Transcript log channel ID not configured. Cannot archive transcript for ${ticketChannel.name}.`);
            await logTicketAction(client, config, `WARNING: Transcript log channel not configured. Cannot archive transcript for ${ticketChannel.name}.`, true);
        }
        return;
    }

    try {
        const transcriptLogChannel = await client.channels.fetch(config.transcripts.logChannelId);
        if (transcriptLogChannel && transcriptLogChannel.isTextBased()) {
            // Extract ticket number from embed if possible for better logging
            const messages = await ticketChannel.messages.fetch({ limit: 10 });
            const botTicketMessage = messages.find(m => m.author.id === client.user.id && m.embeds.length > 0 && m.embeds[0].title?.includes("Ticket #"));
            const ticketNumberFromEmbed = botTicketMessage?.embeds[0]?.title?.split('#')[1]?.split(' ')[0] || 'N/A';

            await transcriptLogChannel.send({
                content: `Transcript for closed ticket: ${ticketChannel.name} (Ticket #${ticketNumberFromEmbed}, closed by ${closerUser.tag})`,
                files: [transcriptAttachment]
            });
            await logTicketAction(client, config, `Transcript for ${ticketChannel.name} archived in ${transcriptLogChannel.name}.`);
        } else {
            console.warn(`Transcript log channel ID ${config.transcripts.logChannelId} not found or not text-based.`);
            await logTicketAction(client, config, `WARNING: Transcript log channel ${config.transcripts.logChannelId} not found for ${ticketChannel.name}.`, true);
            if (transcriptAttachment){ 
                try {
                    await ticketChannel.send({ content: `Transcript generated (log channel not found/configured).`, files: [transcriptAttachment] });
                } catch (e) { console.error(`Error sending transcript to ticket channel ${ticketChannel.name} as fallback:`, e); }
            }
        }
    } catch (logError) {
        console.error(`Failed to send transcript to log channel ${config.transcripts.logChannelId}:`, logError);
        await logTicketAction(client, config, `ERROR: Failed to send transcript for ${ticketChannel.name} to log channel. Error: ${logError.message}`, true);
    }
}

/**
 * Deletes a local transcript file.
 * @param {string} filePath The path to the transcript file.
 * @param {import('discord.js').Client} client The Discord client.
 * @param {object} config The bot's configuration object.
 */
export async function deleteTranscriptFile(filePath, client, config) {
    if (!filePath) return;
    fs.unlink(filePath, (err) => {
        if (err) {
            console.error(`Error deleting transcript file ${filePath}:`, err);
            logTicketAction(client, config, `ERROR: Failed to delete transcript file ${filePath}. Error: ${err.message}`, true).catch(console.error);
        } else {
            console.log(`Transcript file ${filePath} deleted.`);
        }
    });
}